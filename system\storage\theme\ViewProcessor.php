<?php

namespace Theme25;

/**
 * Клас за обработка на заявки към изгледи
 */
class ViewProcessor extends BaseProcessor {

    /**
     * Път до шаблоните в темата
     *
     * @var string
     */
    protected $templatePath;
    protected $templateCache;
    protected $route;

    /**
     * Конструктор
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $controllerPath Път до контролерите ('Backend' или 'Frontend')
     */
    public function __construct($registry, $controllerPath = 'Frontend') {
        parent::__construct($registry, $controllerPath);
        if($controllerPath == 'Backend') {
            $this->templateCache = false;
        }
        else {
            $this->templateCache = $this->getConfig('template_cache');
        }

        $this->templateCache = false; // временно

        // Задаване на пътя до шаблоните в темата
        $this->templatePath = DIR_THEME . $controllerPath . '/View/Template';
    }

    /**
     * Обработва заявка към изглед
     *
     * @param string $route Път към изгледа
     * @param array $data Данни за изгледа
     * @return string Резултат от рендирането на изгледа
     */

     public function process($route, $data = array()) {
        $this->route = $route;
        // Подготовка на пътя и тригера
        $route = $this->sanitizeRoute($route);
        $trigger = $route;

        // Обработка на събитията преди рендиране
        $preEventResult = $this->triggerPreEvents($trigger, $route, $data);

        if ($preEventResult !== null) {
            $output = $preEventResult;
        } else {
            // Рендиране на изгледа
            $output = $this->renderView($route, $data);
        }

        // Обработка на събитията след рендиране
        $postEventResult = $this->triggerPostEvents($trigger, $route, $data, $output);

        if ($postEventResult !== null) {
            $output = $postEventResult;
        }

        return $output;
    }

    /**
     * Санитизира пътя към изгледа
     *
     * @param string $route Път към изгледа
     * @return string Санитизиран път
     */
    protected function sanitizeRoute($route) {
        $sanitized = preg_replace('/[^a-zA-Z0-9_\/]/', '', (string)$route);
        return $sanitized;
    }

    /**
     * Обработва събитията преди рендиране на изгледа
     *
     * @param string $trigger Оригинален тригер
     * @param string $route Път към изгледа
     * @param array $data Данни за изгледа
     * @return mixed Резултат от събитието или null
     */
    protected function triggerPreEvents($trigger, &$route, &$data) {
        $result = $this->registry->get('event')->trigger('view/' . $trigger . '/before', array(&$route, &$data));

        if ($result && !$result instanceof \Exception) {
            return $result;
        }

        return null;
    }

    /**
     * Рендира изгледа
     *
     * @param string $route Път към изгледа
     * @param array $data Данни за изгледа
     * @return string Рендиран изглед
     */
    protected function renderView($route, $data) {
        // Проверка за персонализиран изглед в темата
        $themeViewFile = $this->getThemeViewPath($route);

        if (file_exists($themeViewFile)) {
            // Извличане на пътя без разширението
            $fileDirectory = pathinfo($themeViewFile, PATHINFO_DIRNAME);
            $fileNameWithoutExtension = pathinfo($themeViewFile, PATHINFO_FILENAME);
            $themeViewPath = $fileDirectory . '/' . $fileNameWithoutExtension;
            return $this->renderThemeView($themeViewPath, $data);
        } else {
            return $this->renderStandardView($route, $data);
        }
    }

    /**
     * Връща пътя до изгледа в темата
     *
     * @param string $route Път към изгледа
     * @return string Път до файла на изгледа в темата
     */
    protected function getThemeViewPath($route) {
        $path = $this->templatePath . '/' . $route . '.twig';
        return $path;
    }

    /**
     * Връща относителния път до изгледа в темата
     *
     * @param string $route Път към изгледа
     * @return string Относителен път до файла на изгледа в темата
     */
    protected function getRelativeThemeViewPath($route) {
        $path = $route;
        return $path;
    }

    /**
     * Рендира персонализиран изглед от темата
     *
     * @param string $themeViewPath Път до файла на изгледа в темата
     * @param array $data Данни за изгледа
     * @return string Рендиран изглед
     */
    protected function renderThemeView($themeViewPath, $data) {
        // Проверка дали трябва да използваме общия шаблон
        if ( ($this->controllerPath === 'Backend' || $this->controllerPath === 'Frontend') && !$this->isStandaloneTemplate($themeViewPath, $data)) {
            return $this->renderWithLayout($themeViewPath, $data);
        } else {
            // Стандартно рендиране без общ шаблон
            $template = $this->createTemplate();

            // Добавяне на данните към шаблона
            $this->setTemplateData($template, $data);
            try {
                // Рендиране на шаблона
                $result = $template->render($themeViewPath, $this->isTemplateCaching());
                return $result;
            } catch (\Exception $e) {
                return 'Error rendering theme view: ' . $themeViewPath . ' - ' . $e->getMessage();
            }
        }
    }

    /**
     * Рендира изглед, използвайки общия шаблон layout.twig
     *
     * @param string $themeViewPath Път до файла на изгледа в темата
     * @param array $data Данни за изгледа
     * @return string Рендиран изглед с общия шаблон
     */
    protected function renderWithLayout($themeViewPath, $data) {
        try {
            // Първо рендираме съдържанието на конкретния шаблон
            $template = $this->createTemplate();
            $this->setTemplateData($template, $data);

            $content = $template->render($themeViewPath, $this->isTemplateCaching());

            // След това рендираме общия шаблон, като подаваме съдържанието
            $layoutTemplate = $this->createTemplate();

            // Копираме всички данни от оригиналния шаблон
            $layoutData = $data;

            // Добавяме съдържанието като 'content'
            $layoutData['content'] = $content;

            // Проверка и зареждане на компонентите, ако не съществуват
            $this->loadLayoutComponents($layoutData);
            // Задаваме данните на шаблона
            $this->setTemplateData($layoutTemplate, $layoutData);

            // Рендираме общия шаблон
            $layoutPath = $this->templatePath . '/common/layout';

            $result = $layoutTemplate->render($layoutPath, $this->isTemplateCaching());


            return $result;
        } catch (\Exception $e) {
            return 'Error rendering with layout: ' . $e->getMessage();
        }
    }

    private function isTemplateCaching() {
        return $this->templateCache;
    }

    /**
     * Проверява дали шаблонът трябва да се рендира самостоятелно
     *
     * @param string $themeViewPath Път до файла на изгледа в темата
     * @return bool Връща true ако шаблонът трябва да се рендира самостоятелно
     */
    protected function isStandaloneTemplate($themeViewPath, $data) {
        if (isset($data['__STANDALONE__']) && $data['__STANDALONE__']) {
            return true;
        }

        // Списък с шаблони, които трябва да се рендират самостоятелно
        $standaloneTemplates = [
            'common/sidebar',
            'common/header',
            'common/column_left',
            'common/column_right',
            'common/footer',
            'common/layout',
            'common/login',
            // 'error/not_found',
            // 'error/permission'
        ];

        foreach ($standaloneTemplates as $template) {
            if (strpos($themeViewPath, $template) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Рендира стандартен изглед
     *
     * @param string $route Път към изгледа
     * @param array $data Данни за изгледа
     * @return string Рендиран изглед
     */
    protected function renderStandardView($route, $data) {
        // Проверка дали трябва да използваме общия шаблон
        if (($this->controllerPath === 'Backend' || $this->controllerPath === 'Frontend') && !$this->isStandaloneRoute($route)) {
            try {
                // Първо рендираме съдържанието на стандартния шаблон
                $template = $this->createTemplate();
                $this->setTemplateData($template, $data);
                $templatePath = $this->getConfig('template_directory') . $route;

                $content = $template->render($templatePath, $this->isTemplateCaching());

                // След това рендираме общия шаблон, като подаваме съдържанието
                return $this->renderWithStandardContent($content, $data);
            } catch (\Exception $e) {
                return 'Error rendering standard view with layout: ' . $route . ' - ' . $e->getMessage();
            }
        } else {
            // Стандартно рендиране без общ шаблон
            $template = $this->createTemplate(false);

            // Добавяне на данните към шаблона
            $this->setTemplateData($template, $data);

            try {
                // Рендиране на шаблона
                $templatePath = $this->getConfig('template_directory') . $route;
                $result = $template->render($templatePath, $this->isTemplateCaching());
                return $result;
            } catch (\Exception $e) {
                return 'Error rendering standard view: ' . $route . ' - ' . $e->getMessage();
            }
        }
    }

    /**
     * Рендира общия шаблон със стандартно съдържание
     *
     * @param string $content Рендирано съдържание
     * @param array $data Данни за изгледа
     * @return string Рендиран изглед с общия шаблон
     */
    protected function renderWithStandardContent($content, $data) {
        try {
            // Създаваме шаблон за общия layout
            $layoutTemplate = $this->createTemplate();

            // Копираме всички данни от оригиналния шаблон
            $layoutData = $data;

            // Добавяме съдържанието като 'content'
            $layoutData['content'] = $content;

            // Проверка и зареждане на компонентите, ако не съществуват
            $this->loadLayoutComponents($layoutData);

            // Задаваме данните на шаблона
            $this->setTemplateData($layoutTemplate, $layoutData);

            // Рендираме общия шаблон
            $layoutPath = $this->templatePath . '/common/layout';

            $result = $layoutTemplate->render($layoutPath, $this->isTemplateCaching());
            return $result;
        } catch (\Exception $e) {
            return 'Error rendering with standard content: ' . $e->getMessage();
        }
    }

    /**
     * Проверява дали маршрутът трябва да се рендира самостоятелно
     *
     * @param string $route Път към изгледа
     * @return bool Връща true ако маршрутът трябва да се рендира самостоятелно
     */
    protected function isStandaloneRoute($route) {
        // Списък с маршрути, които трябва да се рендират самостоятелно
        $standaloneRoutes = [
            'common/sidebar',
            'common/header',
            'common/column_left',
            'common/column_right',
            'common/footer',
            'common/layout',
            'common/login',
            'error/not_found',
            'error/permission'
        ];

        foreach ($standaloneRoutes as $template) {
            if (strpos($route, $template) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Създава нов обект Template
     *
     * @return \Template Обект Template
     */
    protected function createTemplate($add_theme_path = true) {
        $templateEngine = $this->getConfig('template_engine');

        // Дефиниране на константа DIR_THEME_TEMPLATE, ако не е дефинирана
        if (!defined('DIR_THEME_TEMPLATE')) {
            define('DIR_THEME_TEMPLATE', DIR_THEME . $this->controllerPath . '/View/Template/');
        }

        // Създаваме нов обект Template
        $template = new \Template($templateEngine);

        // Ако използваме Twig, добавяме пътя до шаблоните в темата
        if ($templateEngine === 'twig') {
            // Достъпваме Twig адаптера директно
            $twigAdapter = $template->getAdaptor();
            
            // Добавяме пътя до шаблоните в темата
            if (method_exists($twigAdapter, 'addTemplatePath') && $add_theme_path) {
                $twigAdapter->addTemplatePath(DIR_THEME_TEMPLATE);
            }
        }

        return $template;
    }

    /**
     * Задава данните на шаблона
     *
     * @param \Template $template Обект Template
     * @param array $data Данни за изгледа
     * @return void
     */
    protected function setTemplateData($template, $data) {
        foreach ($data as $key => $value) {
            $template->set($key, $value);
        }
    }

    /**
     * Проверява и зарежда компонентите на layout-а, ако не съществуват
     *
     * @param array &$layoutData Данни за layout-а
     * @return void
     */
    protected function loadLayoutComponents(&$layoutData) {

        $this->prepareDocumentData($layoutData);

        // Проверка и зареждане на header
        if (!isset($layoutData['header'])) {
            $layoutData['header'] = $this->loadComponent('common/header');
        }

        // Проверка и зареждане на sidebar
        if (!isset($layoutData['sidebar'])) {
            $layoutData['sidebar'] = $this->loadComponent('common/sidebar');
        }

        // Проверка и зареждане на footer
        if (!isset($layoutData['footer'])) {
            $layoutData['footer'] = $this->loadComponent('common/footer');
        }
    }

    /**
     * Подготвя основните данни за документа
     *
     */
    protected function prepareDocumentData(&$layoutData) {
        $class = '\Theme25\\' . $this->controllerPath . '\Controller\Common\Base';
        $base_controller = new $class($this->registry, $this->route);   
        $base_controller->prepareDocumentData($layoutData);
    }

    /**
     * Зарежда компонент чрез контролер
     *
     * @param string $route Път към контролера
     * @return string Рендиран компонент
     */
    protected function loadComponent($route) {
        // Използваме registry за достъп до load обекта
        $load = $this->registry->get('load');

        // Проверка дали компонентът съществува
        if ($route == 'common/sidebar' && !class_exists('\\Theme25\\Backend\\Controller\\Common\\SideBar')) {
            // Ако SideBar не съществува, опитваме с Column_Left
            $route = 'common/column_left';
        }

        try {
            // Зареждаме компонента чрез контролер
            $result = $load->controller($route);
            return $result;
        } catch (\Exception $e) {
            return 'Error loading component: ' . $route;
        }
    }


    /**
     * Обработва събитията след рендиране на изгледа
     *
     * @param string $trigger Оригинален тригер
     * @param string $route Път към изгледа
     * @param array $data Данни за изгледа
     * @param string $output Рендиран изглед
     * @return mixed Резултат от събитието или null
     */
    protected function triggerPostEvents($trigger, &$route, &$data, &$output) {
        $result = $this->registry->get('event')->trigger('view/' . $trigger . '/after', array(&$route, &$data, &$output));

        if ($result && !$result instanceof \Exception) {
            return $result;
        }

        return null;
    }
}




