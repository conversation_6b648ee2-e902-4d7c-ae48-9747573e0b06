[28-Aug-2025 15:36:50 UTC] PHP Warning:  Version warning: <PERSON>magick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:37:12 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:37:21 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:37:57 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:41:04 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:41:34 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:41:56 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:42:16 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:46:03 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:46:15 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:46:23 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:46:49 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:53:14 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:53:19 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:53:28 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:53:37 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:55:03 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 15:56:00 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 18:56:04 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function isLogged() on null in /home/<USER>/storage_theme25/theme/Controller.php:354
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(414): Theme25\Controller->isUserLogged()
#1 /home/<USER>/storage_theme25/theme/Controller.php(526): Theme25\Controller->checkAdminAccess()
#2 /home/<USER>/storage_theme25/theme/Controller.php(30): Theme25\Controller->checkFrontendAccessRestriction()
#3 /home/<USER>/storage_theme25/theme/Frontend/Controller/Common/Home.php(8): Theme25\Controller->__construct(Object(Registry), 'common/home')
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(267): Theme25\Frontend\Controller\Common\Home->__construct(Object(Registry))
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCommo...', 'index', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCommo...', 'index', Array)
#7 /home/<USER>/storage_theme25/ in /home/<USER>/storage_theme25/theme/Controller.php on line 354
[28-Aug-2025 15:58:33 UTC] PHP Warning:  Version warning: Imagick was compiled against ImageMagick version 1692 but version 1693 is loaded. Imagick will run but may behave surprisingly in Unknown on line 0
[28-Aug-2025 18:58:33 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function isLogged() on null in /home/<USER>/storage_theme25/theme/Controller.php:354
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(414): Theme25\Controller->isUserLogged()
#1 /home/<USER>/storage_theme25/theme/Controller.php(526): Theme25\Controller->checkAdminAccess()
#2 /home/<USER>/storage_theme25/theme/Controller.php(30): Theme25\Controller->checkFrontendAccessRestriction()
#3 /home/<USER>/storage_theme25/theme/Frontend/Controller/Common/Home.php(8): Theme25\Controller->__construct(Object(Registry), 'common/home')
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(267): Theme25\Frontend\Controller\Common\Home->__construct(Object(Registry))
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCommo...', 'index', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCommo...', 'index', Array)
#7 /home/<USER>/storage_theme25/ in /home/<USER>/storage_theme25/theme/Controller.php on line 354
