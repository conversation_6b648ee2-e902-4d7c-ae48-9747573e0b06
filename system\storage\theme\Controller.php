<?php

namespace Theme25;

class Controller extends \Controller
{

    protected $_language = '';
    protected $_current_language = '';
    protected $_route = '';
    public $data = [];
    public $languages = [];
    public $active_language_id = 0;

    public function __construct($registry, $route = '')
    {
        if (is_object($registry) && !($registry instanceof \Registry)) {
            $registry = \is_callable([$registry, 'getRegistry']) ? $registry->getRegistry() : null;
            if (!$registry)
                throw new \Exception('Registry object not found in parent controller!');
        }
        parent::__construct($registry);
        $this->_route = $route;

        // Инициализиране на данните
        $this->data = [];
        $this->prepareLanguageData();
    }

    public function setTitle($title)
    {
        $this->document->setTitle($title);
        $this->setData('heading_title', $title);
    }

    public function loadLanguage($route)
    {
        $this->load->language($route);
        $this->_current_language = $this->_route;
    }

    public function getLanguageText($key, $route = '')
    {
        if ($route)
            $this->loadLanguage($route);
        else
            $this->setDefaultLanguage();
        return $this->language->get($key);
    }

    public function setDefaultLanguage()
    {
        if ($this->_current_language != $this->_language && $this->_route) {
            $this->_language = $this->_route;
            $this->loadLanguage($this->_language);
        }
    }

    public function getLanguageId()
    {
        return $this->active_language_id;
    }

    protected function prepareLanguageData()
    {
        $this->loadModelAs('localisation/language', 'languageModel');
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');

        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        $languages_data = $this->languageModel->getLanguages();
        $languages = [];
        $language_exists = false;

        foreach ($languages_data as $language) {
            $languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
            }
        }

        $this->languages = $languages;

        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->active_language_id = $active_language_id;
    }

    public function loadController($controller, $data = array())
    {
        return $this->load->controller($controller, $data);
    }

    public function loadModel($route)
    {
        $this->load->model($route);
    }

    public function loadModels($routes)
    {
        foreach ($routes as $route) {
            $this->load->model($route);
        }
    }

    /**
     * Зарежда модел и го прави достъпен чрез персонализирана променлива
     *
     * @param string $route Път до модела (напр. 'catalog/product')
     * @param string $alias Име на променливата, чрез която ще се достъпва модела (напр. 'productModel')
     * @return void
     */
    public function loadModelAs($route, $alias)
    {

        // Проверка дали името не съвпада с резервирани имена
        $reservedNames = ['request', 'session', 'config', 'load', 'response', 'url', 'user', 'language', 'document', 'currency'];
        if (in_array($alias, $reservedNames)) {
            // Получаване на backtrace за по-подробна информация
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
            $caller = isset($backtrace[1]) ? $backtrace[1] : [];
            $callerClass = isset($caller['class']) ? $caller['class'] : 'Unknown';
            $callerMethod = isset($caller['function']) ? $caller['function'] : 'Unknown';
            $callerFile = isset($caller['file']) ? $caller['file'] : 'Unknown';
            $callerLine = isset($caller['line']) ? $caller['line'] : 'Unknown';

            // Генериране на по-подробно съобщение за грешка
            $errorMessage = "Името '{$alias}' е резервирано и не може да се използва като псевдоним за модел.\n";
            $errorMessage .= "Извикано от: {$callerClass}::{$callerMethod} в {$callerFile} на ред {$callerLine}.\n";
            $errorMessage .= "Моля, използвайте друго име за псевдоним за модела '{$route}'.";

            trigger_error($errorMessage, E_USER_WARNING);
            return;
        }



        try {
            // Зареждане на модела
            $load = $this->registry->get('load');
            if ($load) {
                $load->model($route);
            } else {
                F()->log->developer('System load object not found! Trying to load model: ' . $route, __FILE__, __LINE__);
                return;
            }


            //$this->load->model($route);
        } catch (\Exception $e) {
            F()->log->developer($e->getMessage(), __FILE__, __LINE__);
            return;
        }

        // Създаване на референция към модела с новото име
        $modelName = 'model_' . str_replace('/', '_', $route);
        $this->$alias = $this->$modelName;
    }

    /**
     * Зарежда множество модели и ги прави достъпни чрез персонализирани променливи
     *
     * @param array $modelMap Асоциативен масив с път до модела като ключ и име на променливата като стойност
     * @return void
     */
    public function loadModelsAs($modelMap)
    {
        foreach ($modelMap as $route => $alias) {
            $this->loadModelAs($route, $alias);
        }
    }

    public function hasModel($instance_name)
    {
        return !empty($this->$instance_name);
    }

    public function loadView($route, $data = array())
    {
        return $this->load->view($route, $data);
    }

    public function getConfig($key)
    {
        $config = $this->config->get($key);
        $config_from_second_db = $this->getConfigFromSecondDB($key);
        if ($config === NULL && $config_from_second_db !== NULL) {
            return $config_from_second_db;
        }
        return $config;
    }


    /**
     * Връща конфигурационни стойности от втората база данни, ако е налична
     * Ако втората база данни не е дефинирана или не е достъпна, връща конфигурациите от основната база данни
     *
     * @param string $key Ключ на конфигурационната стойност
     * @return mixed Конфигурационна стойност или null, ако не съществува
     */
    public function getConfigFromSecondDB($key)
    {
        try {
            // Проверка дали втората база данни е разрешена в .env файла
            $envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
            $envLoader = new \Theme25\EnvLoader($envPath);

            if (!$envLoader->get('SECOND_DB_ENABLED', false)) {
                // Ако втората база данни не е разрешена, връщаме конфигурацията от основната база данни
                return $this->config->get($key);
            }

            // Проверка дали втората база данни е инициализирана в BaseProcessor
            $reflection = new \ReflectionClass('\Theme25\BaseProcessor');
            $secondDbProperty = $reflection->getProperty('secondDb');
            $secondDbProperty->setAccessible(true);
            $secondDb = $secondDbProperty->getValue();

            if ($secondDb === null) {
                // Ако втората база данни не е инициализирана, връщаме конфигурацията от основната база данни
                return $this->config->get($key);
            }

            // Запазване на текущата конфигурация
            $currentConfig = $this->config->get($key);

            // Превключване към конфигурацията на втората база данни
            \Theme25\ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Извличане на конфигурационната стойност от втората база данни
            $secondDbConfig = $this->config->get($key);

            // Превключване обратно към конфигурацията на първата база данни
            \Theme25\ConfigManager::switchToFirstDbConfig($this->registry);

            // Връщане на конфигурацията от втората база данни, ако съществува
            // В противен случай връщаме конфигурацията от основната база данни
            return $secondDbConfig !== null ? $secondDbConfig : $currentConfig;

        } catch (\Exception $e) {
            // При възникване на грешка, записваме я в лог файла
            if (class_exists('\Log')) {
                $log = new \Log('second_db_config_error.log');
                $log->write('Грешка при достъп до конфигурация от втората база данни: ' . $e->getMessage());
            }

            // Връщаме конфигурацията от основната база данни като fallback
            return $this->config->get($key);
        }
    }

    public function getUserToken()
    {
        return $this->session->data['user_token'];
    }

    public function getRegistry($key = null)
    {
        return $this->registry ? ($key ? $this->registry->get($key) : $this->registry) : null;
    }

    public function setRegistry($key, $default = null)
    {
        $this->registry->set($key, $default);
    }

    /**
     * Инициализира основни данни за административния панел и ги добавя в $this->data
     *
     * @param bool $returnData Дали да върне данните като масив
     * @return array|$this Масив с данни или текущия обект за верижно извикване
     */
    public function initAdminData($returnData = false)
    {
        $adminData = [
            'user_token' => $this->getUserToken()
        ];

        // Добавяме данните в $this->data
        $this->setData($adminData);

        if ($returnData) {
            return $adminData;
        }

        return $this;
    }

    public function addBackendScriptWithVersion($scriptNames, $position = 'footer')
    {

        if (!is_array($scriptNames)) {
            $scriptNames = [$scriptNames];
        }

        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;

        foreach ($scriptNames as $scriptName) {
            $scriptUrl = $base . 'backend_js/' . $scriptName;
            $scriptPath = DIR_THEME . 'Backend/View/Javascript/' . $scriptName;
            if (file_exists($scriptPath)) {
                $lastModified = filemtime($scriptPath);
                $scriptUrl .= '?v=' . $lastModified;
                $this->document->addScript($scriptUrl, $position);
            }
        }
    }


    public function getSession($key = null)
    {
        return isset($this->session->data[$key]) ? $this->session->data[$key] : $this->session->data;
    }

    public function setSession($key, $value)
    {
        $this->session->data[$key] = $value;
    }

    public function unsetSession($key)
    {
        if (isset($this->session->data[$key])) {
            unset($this->session->data[$key]);
        }
    }

    public function setResponseOutput($output)
    {
        $this->response->setOutput($output);
    }

    public function setJSONResponseOutput($output)
    {
        $this->response->addHeader('Content-Type: application/json');
        $this->setResponseOutput(json_encode($output));
    }


    public function isUserLogged()
    {
        return $this->user->isLogged();
    }


    /**
     * Проверява дали заявката е AJAX
     *
     * @return bool True ако е AJAX заявка
     */
    public function isAjaxRequest()
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    }

    public function isAjax() {
        return $this->isAjaxRequest();
    }

    public function checkAjaxRequest() {
        if (!$this->isAjaxRequest()) {
            $this->jsonResponse(['error' => 'Невалидна заявка']);
            exit;
        }
        $this->processAjaxPost();
    }

    public function checkPermission($type, $route) {
        if (!$this->hasPermission($type, $route)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'Нямате права за тази операция']);
                exit;
            }
            $this->redirectResponse($this->getAdminLink('common/dashboard'));
            exit;
        }
    }

    /**
     * Връща JSON отговор
     *
     * @param array $data Данни за отговора
     */
    public function jsonResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Проверява дали потребителят има активна административна сесия
     * Използва се за временно ограничение на достъпа до Frontend частта
     *
     * @param bool $showAccessDeniedPage Дали да покаже страница за отказан достъп (по подразбиране true)
     * @return bool True ако има административен достъп, false в противен случай
     */
    public function checkAdminAccess($showAccessDeniedPage = true)
    {
        // Проверяваме дали потребителят е логнат като администратор
        if (!$this->isUserLogged()) {
            if ($showAccessDeniedPage) {
                $this->showAccessDeniedPage();
                exit;
            }
            return false;
        }

        // Проверяваме дали има валиден user_token в сесията
        $sessionToken = $this->getSession('user_token');
        if (empty($sessionToken)) {
            if ($showAccessDeniedPage) {
                $this->showAccessDeniedPage();
                exit;
            }
            return false;
        }

        return true;
    }

    /**
     * Показва страница за отказан достъп до Frontend частта
     */
    private function showAccessDeniedPage()
    {
        // Задаваме HTTP статус 403 Forbidden
        http_response_code(403);

        // Задаваме заглавие на страницата
        $this->setTitle('Достъпът е ограничен');

        // Подготвяме данни за шаблона
        $this->setData([
            'heading_title' => 'Достъпът е временно ограничен',
            'text_message' => 'Публичната част на сайта е временно недостъпна за неоторизирани потребители.',
            'text_admin_login' => 'За достъп, моля влезте в административния панел:',
            'admin_login_url' => $this->getAdminLoginUrl(),
            'text_admin_login_link' => 'Вход в административния панел'
        ]);

        // Рендираме шаблона за отказан достъп
        $this->renderTemplateWithDataAndOutput('error/access_denied');
    }

    /**
     * Връща URL към страницата за вход в административния панел
     *
     * @return string URL към admin login
     */
    private function getAdminLoginUrl()
    {
        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;
        return $base . 'admin/';
    }

    /**
     * Проверява дали потребителят има права за достъп до определен маршрут
     *
     * @param string $type Тип на правото (access, modify и т.н.)
     * @param string $route Маршрут за проверка
     * @return boolean Връща true, ако потребителят има права за достъп
     */
    public function hasPermission($type, $route)
    {
        return $this->user->hasPermission($type, $route);
    }

    public function redirectResponse($url)
    {
        return $this->response->redirect($url);
    }

    public function getLink($route, $args = '', $secure = true)
    {
        // Запазване на текущите query параметри
        $current_url = $_SERVER['REQUEST_URI'];
        $query_params = [];

        // Извличане на query параметрите от текущия URL
        $url_parts = parse_url($current_url);
        if (isset($url_parts['query'])) {
            parse_str($url_parts['query'], $query_params);
        }

        // Премахване на параметрите, които вече са включени в базовия URL
        // или които ще бъдат добавени отново
        $exclude_params = ['route', 'user_token'];

        // Извличане на параметрите от $args
        $args_params = [];
        if (!empty($args)) {
            parse_str($args, $args_params);
        }

        // Добавяне на параметрите от $args към списъка с изключени параметри
        foreach ($args_params as $key => $value) {
            $exclude_params[] = $key;
        }

        // Премахване на изключените параметри
        foreach ($exclude_params as $param) {
            if (isset($query_params[$param])) {
                unset($query_params[$param]);
            }
        }

        // Добавяне на текущите query параметри към $args
        if (!empty($query_params)) {
            $args .= (!empty($args) ? '&' : '') . http_build_query($query_params);
        }

        return str_replace('&amp;', '&', $this->url->link($route, $args, $secure));
    }

    public function getAdminLink($route, $args = '', $secure = true, $remove_query_args = [])
    {
        // Добавяне на user_token
        if ($args)
            $args .= '&user_token=' . $this->getUserToken();
        else
            $args = 'user_token=' . $this->getUserToken();

        // Запазване на текущите query параметри
        $current_url = $_SERVER['REQUEST_URI'];
        $query_params = [];

        // Извличане на query параметрите от текущия URL
        // $url_parts = parse_url($current_url);
        // if (isset($url_parts['query'])) {
        //     parse_str($url_parts['query'], $query_params);
        // }

        // Премахване на параметрите, които вече са включени в базовия URL
        // или които ще бъдат добавени отново
        $exclude_params = array_merge(['route', 'user_token'], $remove_query_args);

        // Извличане на параметрите от $args
        $args_params = [];
        if (!empty($args)) {
            parse_str($args, $args_params);
        }

        // Добавяне на параметрите от $args към списъка с изключени параметри
        foreach ($args_params as $key => $value) {
            $exclude_params[] = $key;
        }

        // Премахване на изключените параметри
        foreach ($exclude_params as $param) {
            if (isset($query_params[$param])) {
                unset($query_params[$param]);
            }
        }

        // Добавяне на текущите query параметри към $args
        if (!empty($query_params)) {
            $args .= '&' . http_build_query($query_params);
        }

        return str_replace('&amp;', '&', $this->url->link($route, $args, $secure));
    }

    /**
     * Връща масив с административни линкове
     *
     * @param array $routes Асоциативен масив с ключове (имена на линковете) и стойности (пътища до контролери)
     * @param array $args Асоциативен масив с допълнителни аргументи за всеки линк (опционално)
     * @param bool $secure Дали линковете да са сигурни (HTTPS)
     * @param bool $preserve_query Дали да се запазват текущите query параметри (по подразбиране: true)
     * @return array Масив с генерирани линкове
     */
    public function getAdminLinks($routes, $args = [], $secure = true, $preserve_query = true)
    {
        $links = [];
        foreach ($routes as $key => $route) {
            $routeArgs = isset($args[$key]) ? $args[$key] : '';
            $links[$key] = $this->getAdminLink($route, $routeArgs, $secure);
        }
        return $links;
    }



    public function isPostRequest()
    {
        return ($this->request->server['REQUEST_METHOD'] == 'POST');
    }

    public function requestPost($key = null, $default = null)
    {
        if ($key === null) {
            return $this->request->post;
        }
        return isset($this->request->post[$key]) ? $this->request->post[$key] : $default;
    }

    public function requestGet($key = null, $default = null)
    {
        if ($key === null) {
            return $this->request->get;
        }
        return isset($this->request->get[$key]) ? $this->request->get[$key] : $default;
    }

    public function requestFiles($key = null, $default = null)
    {
        if ($key === null) {
            return $this->request->files;
        }
        return isset($this->request->files[$key]) ? $this->request->files[$key] : $default;
    }

    public function getServer($key)
    {
        return isset($this->request->server[$key]) ? $this->request->server[$key] : null;
    }

    public function getRoute()
    {
        return $this->request->get['route'];
    }

    public function generateToken($length = 32)
    {
        return token($length);
    }

    public function unsetRequestGet($key)
    {
        if (isset($this->request->get[$key])) {
            unset($this->request->get[$key]);
        }
    }

    public function getinputData()
    {
        $input = file_get_contents('php://input');
        return $input;
    }

    public function getJSONinputData()
    {
        $input = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }
        return $input;
    }

    public function processAjaxPost()
    {
        $post = $this->requestPost();
        $post = !empty($post) ? $post : $this->getJSONinputData();
        $this->request->post = $post;
        return $post;
    }


    public function isDeveloper()
    {
        if (!function_exists('isDeveloper'))
            return false;
        return isDeveloper();
    }

    /**
     * Връща CSS селектор (икона/цвят) по име чрез ThemeStyles
     */
    public function getThemeStyle($name)
    {
        return \Theme25\ThemeStyles::get($name);
    }

    /**
     * Добавя или актуализира данни в $this->data
     *
     * @param mixed $keyOrArray Ключ или асоциативен масив с данни
     * @param mixed $value Стойност (ако $keyOrArray е стринг)
     * @return $this За верижно извикване на методи
     */
    public function setData($keyOrArray, $value = null)
    {
        if (is_array($keyOrArray)) {
            // Ако е подаден масив, обединяваме го с текущите данни
            $this->data = array_merge($this->data, $keyOrArray);
        } else {
            // Ако е подаден ключ и стойност, добавяме/актуализираме стойността
            $this->data[$keyOrArray] = $value;
        }

        return $this;
    }

    /**
     * Връща данни от $this->data
     *
     * @param string|null $key Ключ за извличане на конкретна стойност (опционално)
     * @return mixed Стойност или целия масив с данни
     */
    public function getData($key = null)
    {
        if ($key !== null) {
            return isset($this->data[$key]) ? $this->data[$key] : null;
        }

        return $this->data;
    }

    /**
     * Рендира шаблон с данните от $this->data
     *
     * @param string $route Път до шаблона
     * @param array $additionalData Допълнителни данни (опционално)
     * @return string HTML съдържание
     */
    public function renderTemplate($route, $additionalData = [])
    {
        // Обединяваме текущите данни с допълнителните данни
        $data = array_merge($this->data, $additionalData);
        return $this->loadView($route, $data);
    }

    /**
     * Рендира шаблон и задава резултата като изход
     *
     * @param string $route Път до шаблона
     * @param array $data Допълнителни данни (опционално)
     */
    public function renderTemplateAndOutput($route, $data = array())
    {
        $this->setResponseOutput($this->loadView($route, $data));
    }

    /**
     * Рендира шаблон с данните от $this->data и задава резултата като изход
     *
     * @param string $route Път до шаблона
     * @param array $additionalData Допълнителни данни (опционално)
     */
    public function renderTemplateWithDataAndOutput($route, $additionalData = [])
    {
        $output = $this->renderTemplate($route, $additionalData);
        if (!empty($additionalData['__STANDALONE__']))
            return $output;
        $this->setResponseOutput($output);
    }

    /**
     * Рендира частичен шаблон (без обвиващия общ шаблон)
     *
     * @param string $route Път до шаблона
     * @param array $data Допълнителни данни (опционално)
     * @return string HTML съдържание
     */
    public function renderPartTemplate($route, $data = [])
    {
        $data['__STANDALONE__'] = true;
        return $this->renderTemplateWithDataAndOutput($route, $data);
    }

    /**
     * Форматира валутна стойност
     *
     * Може да се използва по два начина:
     * 1. formatCurrency($amount, $currencyCode, $currencyValue)
     * 2. formatCurrency($amount, $dataArray) - където $dataArray съдържа 'currency_code' и 'currency_value'
     *
     * @param float $amount Сумата за форматиране
     * @param mixed $currencyCodeOrData Код на валутата или масив с данни
     * @param float|null $currencyValue Стойност на валутата (опционално, ако втория параметър е масив)
     * @return string Форматирана валутна стойност
     */
    public function formatCurrency($amount = 0, $currencyCodeOrData = null, $currencyValue = null)
    {
        // Проверка дали втория параметър е масив
        if (is_array($currencyCodeOrData)) {
            $currencyCode = isset($currencyCodeOrData['currency_code']) ? $currencyCodeOrData['currency_code'] : '';
            $currencyValue = isset($currencyCodeOrData['currency_value']) ? $currencyCodeOrData['currency_value'] : 1;
            return $this->currency->format($amount, $currencyCode, $currencyValue);
        } else {
            // Използване на традиционния начин с три отделни параметъра
            $formatted = $this->currency->format($amount, $currencyCodeOrData, $currencyValue);
            return $formatted;
        }
    }

    /**
     * Форматира дата
     *
     * Може да се използва по няколко начина:
     * 1. formatDate($dateString, $format) - директно форматиране с посочен формат
     * 2. formatDate($dateString) - форматиране с формат от езиковия файл 'date_format_short'
     * 3. formatDate($dateString, $formatKey, true) - форматиране с формат от езиковия файл по ключ
     * 4. formatDate($dataArray, $formatKey, $isLanguageKey) - където $dataArray съдържа 'date_added' или 'date'
     *
     * @param mixed $dateStringOrData Дата като стринг или масив с данни
     * @param string $formatOrKey Формат на датата или ключ от езиковия файл
     * @param bool $isLanguageKey Дали форматът е ключ от езиковия файл
     * @return string Форматирана дата
     */
    public function formatDate($dateStringOrData, $formatOrKey = 'date_format_short', $isLanguageKey = true)
    {
        $dateString = '';

        // Проверка дали първия параметър е масив
        if (is_array($dateStringOrData)) {
            if (isset($dateStringOrData['date_added'])) {
                $dateString = $dateStringOrData['date_added'];
            } elseif (isset($dateStringOrData['date'])) {
                $dateString = $dateStringOrData['date'];
            } else {
                return '';
            }
        } else {
            $dateString = $dateStringOrData;
        }

        // Определяне на формата
        $format = $formatOrKey;
        if ($isLanguageKey) {
            $format = $this->getLanguageText($formatOrKey);
        }

        // Форматиране на датата
        return date($format, strtotime($dateString));
    }

    public function setBackendSubController($subcontroller_path = null, $main_controller = null)
    {
        $instance_subcontroller = '\\Theme25\\Backend\\Controller\\' . str_replace('/', '\\', $subcontroller_path);
        // Проверка дали класът съществува
        if (!class_exists($instance_subcontroller)) {
            return false;
        }
        return new $instance_subcontroller($main_controller);
    }

    public function setFrontendSubController($subcontroller_path = null, $main_controller = null)
    {
        $instance_subcontroller = '\\Theme25\\Frontend\\Controller\\' . str_replace('/', '\\', $subcontroller_path);
        // Проверка дали класът съществува
        if (!class_exists($instance_subcontroller)) {
            return false;
        }
        return new $instance_subcontroller($main_controller);
    }

    public function dbQuery($query)
    {
        return $this->db->query($query);
    }

    public function dbEscape($value)
    {
        return $this->db->escape($value);
    }

    public function setLog($filename)
    {
        $this->log = new \Log($filename);
    }

    public function writeLog($message)
    {
        $this->log->write($message);
    }

    public function logDev($message, $file = false, $line = false)
    {
        // F()->log->developer($message, __FILE__, __LINE__);
        // F()->log->developer($message, $file, $line);

        file_put_contents(DIR_LOGS . 'dev_log.txt', print_r($message, true) . PHP_EOL, FILE_APPEND);
    }

    public function __call($method, $arguments)
    {
        $class = get_class($this);
        preg_match('#(Backend|Frontend)#', $class, $matches);
        $theme_part = !empty($matches[1]) ? $matches[1] : 'None';
        $subcontroller_class = str_replace('Theme25\\' . $theme_part . '\\Controller\\', '', $class) . '\\' . ucfirst($method);
        $instance_subcontroller = $theme_part == 'Backend' ? $this->setBackendSubController($subcontroller_class, $this) : $this->setFrontendSubController($subcontroller_class, $this);

        if ($instance_subcontroller) {
            if (is_callable([$instance_subcontroller, 'execute'])) {
                return $instance_subcontroller->execute($arguments);
            } else if (is_callable([$instance_subcontroller, 'index'])) {
                return $instance_subcontroller->index($arguments);
            }
        }

        return null;
    }

}