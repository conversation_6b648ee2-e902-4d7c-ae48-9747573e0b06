<?php

namespace Theme25\Backend\Controller\Sale\Order;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($container) {
        parent::__construct($container);

        // Зареждане на специализирания Total модел
        $this->loadModelAs('extension/total', 'totalModel');

        // Инициализация на OrderSession за изолация от Frontend сесии
        $this->initializeOrderSession();

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }



    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'order-edit.js',
            'delivery-extras.js',
        ]);
    }

    public function prepareOrderForm() {
        $order_id = (int)$this->requestGet('order_id');

        if (!$order_id) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            exit;
        }

        // Запазване на информация за започване на редактиране в OrderSession
        if ($this->order_session) {
            $this->order_session->set('form_preparation', [
                'started_at' => date('Y-m-d H:i:s'),
                'order_id' => $order_id,
                'action' => 'prepare_form'
            ]);
        }

        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderTotals($order_id)
             ->prepareOrderVouchers($order_id)
             ->prepareOrderCoupons($order_id)
             ->prepareCustomerData($order_id)
             ->prepareOrderStatuses()
             ->preparePaymentMethods()
             ->prepareShippingMethods()
             ->prepareCustomerGroups()
             ->prepareShippingExtra()
             ->prepareCountries()
             ->prepareDefaultZonesIfNeeded();


        $this->setData([
            'back_url' => $this->getAdminLink('sale/order'),
            'save_url' => $this->getAdminLink('sale/order/edit', 'order_id=' . $order_id),
            'order_id' => $order_id
        ]);
    }

    /**
     * Обработва актуализацията на поръчката
     *
     * @param int $order_id ID на поръчката
     */
    public function processOrderUpdate($order_id) {
        if (!$this->hasPermission('modify', 'sale/order')) {
            $this->setSession('error', 'Няmate права за редактиране на поръчки');
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return;
        }

        $json = [];

        $this->loadModelAs('sale/order', 'orders');

        try {

            // Получаване на данните от формата
            $data = $this->processAjaxPost();

            $validation = $this->validateOrderData($data);

            // Валидация на данните
            if( $validation === true ) {

                // Забрана за запазване без продукти
                if (empty($data['products']) || count($data['products']) === 0) {
                    $json['error'] = 'Поръчката трябва да съдържа поне един продукт';
                    $this->setJSONResponseOutput($json);
                    return;
                }

                $order_data = $this->prepareOrderDataForUpdate($order_id, $data, $json);

                $this->order_session->set('order_data', $order_data);

                // F()->log->developer($this->order_session->get('order_data'), __FILE__, __LINE__);
                // F()->log->developer($order_data, __FILE__, __LINE__);

                // Актуализиране на поръчката
                $this->orders->editOrder($order_id, $order_data);

                // ТРЯБВА ДА СЕ АКТУАЛИЗИРА ЗАПИС С ДАННИТЕ НА КУРИЕРА В НЕГОВА ТАБЛИЦА преди да се изчистят сесийните данни - данни, които са използвани за изчисляване на доставката с метода getQuote(), както и спесифични записи според куриерския модул, например да се извиква метод editOrder() в съответния Delivery модул.

                // Добавяне на история, ако е променен статуса
                if (isset($data['order_status_id']) && !empty($data['order_status_id'])) {
                    $comment = $data['comment'] ?? '';
                    $notify = isset($data['notify']) ? (bool)$data['notify'] : false;

                    $this->orders->addOrderHistory($order_id, $data['order_status_id'], $comment, $notify);
                }

                if(empty($json['error'])) {
                    $json['success'] = 'Поръчката е актуализирана успешно';
                }
            } else {
                $json['error'] = 'Невалидни данни за актуализиране на поръчката<br>' . $validation;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при актуализиране на поръчката: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }

    private function prepareOrderDataForUpdate($order_id, $data=[], &$json=[]) {
        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderVouchers($order_id)
             ->prepareOrderCoupons($order_id);
        $order_data = array_merge($this->getData(), $data);

        unset($order_data['user_token'], $order_data['notify']);

        $fields_rules = $this->orders->getFieldsRules();

        $data_for_update = [];
        foreach ($fields_rules['order'] as $key => $type) {
            if($key == 'date_added') continue; // не обновяваме date_added
            if (isset($order_data[$key])) {
                $data_for_update[$key] = $order_data[$key];
            }
        }

        foreach ($order_data['products'] as $key => $product) {
            $data_for_update['products'][] = $this->processOrderedProduct($fields_rules,$product);
        }

        $data_for_update['vouchers'] = !empty($order_data['order_vouchers']) ? $order_data['order_vouchers'] : [];

        // КОРЕКЦИЯ: Използване на новите имена на полетата
        // Проверяваме и двете имена за обратна съвместимост
        $voucher_code = !empty($data['voucher']) ? $data['voucher'] : (!empty($data['voucher_code']) ? $data['voucher_code'] : '');
        if(empty($data_for_update['vouchers']) && !empty($voucher_code)) {
           $result = $this->applyVoucher($order_id, $voucher_code);
           if(!empty($result['success'])) {
                $this->prepareOrderVouchers($order_id);
                $data_for_update['vouchers'] = $this->getData('order_vouchers');
           }
           else {
                $json['error'] = $result['error'];
           }
        }

        // КОРЕКЦИЯ: Използване на новите имена на полетата
        // Проверяваме и двете имена за обратна съвместимост
        $coupon_code = !empty($data['coupon']) ? $data['coupon'] : (!empty($data['coupon_code']) ? $data['coupon_code'] : '');
        if(empty($order_data['order_coupon']) && !empty($coupon_code)) {
            $result = $this->applyCoupon($order_id, $coupon_code);
            if(!empty($result['success'])) {
                $this->prepareOrderCoupons($order_id, $coupon_code);
            }
            else {
                $json['error'] = $result['error'];
            }
        }

        $data_for_update['totals'] = [];

        $order_totals = $this->totalModel->calculateTotals($data_for_update['products'], $order_id, true);

        $total_record = 0;
        foreach ($order_totals as $key => $total) {
            $data_for_update['totals'][$key] = [];
            foreach ($fields_rules['order_total'] as $rule_key => $type) {
                if (isset($total[$rule_key])) {
                    $data_for_update['totals'][$key][$rule_key] = $total[$rule_key];
                }
            }

            if( $total['code'] == 'total' ) {
                $total_record = $total['value'];
            }
        }

        $data_for_update['total'] = $total_record;

        return $data_for_update;
    }

    private function processOrderedProduct($fields_rules,$ordered_product) {
        $product = [];
        foreach ($fields_rules['order_product'] as $rule_key => $type) {
            if (isset($ordered_product[$rule_key])) {
                $product[$rule_key] = $ordered_product[$rule_key];
            }
            else if($rule_key == 'total') {
                $product['total'] = $ordered_product['price'] * $ordered_product['quantity'];
            }
        }
        $product['option'] = !empty($ordered_product['options']) ? $ordered_product['options'] : [];
        $product['option'] = $this->processOrderedProductOptions($fields_rules,$product);
        return $product;
    }

    private function processOrderedProductOptions($fields_rules,$ordered_product) {
        $options = [];
        if(!empty($ordered_product['option'])) {
            foreach ($ordered_product['option'] as $option) {
                $option_data = [];
                foreach ($fields_rules['order_option'] as $rule_key => $type) {
                    if (isset($option[$rule_key])) {
                        $value = $option[$rule_key];
                        if($rule_key == 'name' || $rule_key == 'value') {
                            $value = trim($value);
                        }
                        $option_data[$rule_key] = $value;
                    }
                }
                $options[] = $option_data;
            }
        }
        return $options;
    }


    /**
     * Валидира данните за поръчката
     *
     * @param array $data Данни за валидация
     * @return bool Резултат от валидацията
     */
    private function validateOrderData($data) {
        $errors = [];

        // Валидация на задължителни полета
        if (empty($data['firstname'])) {
            $errors[] = 'Името е задължително';
        }

        if (empty($data['lastname'])) {
            $errors[] = 'Фамилията е задължителна';
        }

        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Валиден имейл е задължителен';
        }

        if (!empty($errors)) {
            return implode('<br>', $errors);
        }

        return true;
    }

    /**
     * Подготвя основните данни за поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderData($order_id) {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders'
        ]);

        // Получаване на информацията за поръчката
        $order_info = $this->orders->getOrder($order_id);

        if (!$order_info) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return $this;
        }

        // Подготовка на данните за поръчката
        $order_data = [
            'order_id' => $order_info['order_id'],
            'customer_id' => $order_info['customer_id'],
            'customer_group_id' => $order_info['customer_group_id'],
            'firstname' => $order_info['firstname'],
            'lastname' => $order_info['lastname'],
            'email' => $order_info['email'],
            'telephone' => $order_info['telephone'],
            'fax' => $order_info['fax'],
            'order_status_id' => $order_info['order_status_id'],
            'comment' => $order_info['comment'],
            'total' => $order_info['total'],
            'currency_code' => $order_info['currency_code'],
            'currency_value' => $order_info['currency_value'],
            'date_added' => $order_info['date_added'],
            'date_modified' => $order_info['date_modified'],

            // Адрес за плащане
            'payment_firstname' => $order_info['payment_firstname'],
            'payment_lastname' => $order_info['payment_lastname'],
            'payment_company' => $order_info['payment_company'],
            'payment_address_1' => $order_info['payment_address_1'],
            'payment_address_2' => $order_info['payment_address_2'],
            'payment_city' => $order_info['payment_city'],
            'payment_postcode' => $order_info['payment_postcode'],
            'payment_country_id' => !empty($order_info['payment_country_id']) ? $order_info['payment_country_id'] : 33,
            'payment_zone_id' => !empty($order_info['payment_zone_id']) ? $order_info['payment_zone_id'] : 0,
            'payment_method' => $order_info['payment_method'],
            'payment_code' => $order_info['payment_code'],

            // Адрес за доставка
            'shipping_firstname' => $order_info['shipping_firstname'],
            'shipping_lastname' => $order_info['shipping_lastname'],
            'shipping_email' => isset($order_info['shipping_email']) ? $order_info['shipping_email'] : '',
            'shipping_telephone' => isset($order_info['shipping_telephone']) ? $order_info['shipping_telephone'] : '',
            'shipping_company' => $order_info['shipping_company'],
            'shipping_address_1' => $order_info['shipping_address_1'],
            'shipping_address_2' => $order_info['shipping_address_2'],
            'shipping_city' => $order_info['shipping_city'],
            'shipping_postcode' => $order_info['shipping_postcode'],
            'shipping_country_id' => !empty($order_info['shipping_country_id']) ? $order_info['shipping_country_id'] : 33,
            'shipping_zone_id' => !empty($order_info['shipping_zone_id']) ? $order_info['shipping_zone_id'] : 0,
            'shipping_method' => $order_info['shipping_method'],
            'shipping_code' => $order_info['shipping_code']
        ];

        LogDeveloper_($order_data, __FILE__, __LINE__);  // LOG !!!!!!!

        $this->setData($order_data);

        return $this;
    }

    /**
     * Подготвя продуктите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderProducts($order_id) {
        // Получаване на продуктите в поръчката
        $order_products = $this->orders->getOrderProducts($order_id);

        // Зареждане на модела за изображения
        $this->loadModelAs('tool/image', 'imageModel');
        $this->loadModelAs('catalog/product', 'productModel');

        $products = [];

        foreach ($order_products as $product) {
            // Получаване на опциите за продукта
            $options = $this->orders->getOrderOptions($order_id, $product['order_product_id']);

            $product_options = [];
            foreach ($options as $option) {
                $product_options[] = [
                    'name' => trim($option['name']),
                    'value' => $option['value'],
                    'product_option_id' => $option['product_option_id'],
                    'product_option_value_id' => $option['product_option_value_id']
                ];
            }

            // Получаване на изображението на продукта
            $product_info = $this->productModel->getProduct($product['product_id']);
            $product_image = 'no_image.png';

            if ($product_info && !empty($product_info['image'])) {
                $product_image = $product_info['image'];
            }

            // Генериране на композитен ID за съществуващия продукт
            $composite_product_id = $this->order_session ?
                $this->order_session->generateCompositeProductId($product['product_id'], $product_options) :
                $product['order_product_id']; // Fallback към реалния ID ако няма OrderSession

            $products[] = [
                'order_product_id' => $product['order_product_id'], // Реален ID за връзка с БД
                'composite_product_id' => $composite_product_id, // Композитен ID за JavaScript операции
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'quantity' => $product['quantity'],
                'price' => $product['price'],
                'total' => $product['total'],
                'tax' => $product['tax'],
                'reward' => $product['reward'],
                'options' => $product_options,
                'image' => $this->imageModel->resize($product_image, 80, 80),
                'image_thumb' => $this->imageModel->resize($product_image, 50, 50)
            ];
        }

        $this->setData('order_products', $products);

        return $this;
    }

    /**
     * Подготвя общите суми на поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderTotals($order_id) {
        $totals = $this->totalModel->calculateTotals(null, $order_id);
        $this->setData('order_totals', $totals);
        return $this;
    }

    /**
     * Подготвя ваучерите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderVouchers($order_id) {
        // Получаване на ваучерите в поръчката
        $order_vouchers = $this->orders->getOrderVouchers($order_id);

        $vouchers = [];
        $voucher_code = '';

        foreach ($order_vouchers as $voucher) {
            $vouchers[] = [
                'voucher_id' => $voucher['voucher_id'],
                'description' => $voucher['description'],
                'code' => $voucher['code'],
                'from_name' => $voucher['from_name'],
                'from_email' => $voucher['from_email'],
                'to_name' => $voucher['to_name'],
                'to_email' => $voucher['to_email'],
                'amount' => $voucher['amount'],
                'amount_text' => $this->formatCurrency($voucher['amount'], $this->getData('currency_code'), $this->getData('currency_value'))
            ];

            // Запазване на първия ваучер код за полето във формата
            if (empty($voucher_code)) {
                $voucher_code = $voucher['code'];
            }
        }

        $this->setData([
            'order_vouchers' => $vouchers,
            'voucher_code' => $voucher_code
        ]);

        return $this;
    }

    /**
     * Подготвя купоните в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderCoupons($order_id, $coupon_code = '') {
        // Получаване на купон информация от order_totals таблицата
        // $coupon_code = '';
        $coupon_info = [];

        // Търсене на купон в order_totals
        $order_totals = $this->order_session->getOrderTotals();

        foreach ($order_totals as $total) {
            if ($total['code'] === 'coupon') {
                // Извличане на купон кода от заглавието
                if (preg_match('/\(([^)]+)\)/', $total['title'], $matches)) {
                    $coupon_code = $matches[1];
                }

                $coupon_info = [
                    'code' => $coupon_code,
                    'title' => $total['title'],
                    'value' => $total['value'],
                    'value_text' => $this->formatCurrency($total['value'], $this->getData('currency_code'), $this->getData('currency_value'))
                ];
                break;
            }
        }

        $this->setData([
            'order_coupon' => $coupon_info,
            'coupon_code' => $coupon_code
        ]);

        return $this;
    }

    /**
     * Подготвя данните за клиента
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareCustomerData($order_id) {
        $customer_id = $this->getData('customer_id');

        if ($customer_id) {
            $this->loadModelAs('customer/customer', 'customers');
            $customer_info = $this->customers->getCustomer($customer_id);

            if ($customer_info) {
                $this->setData('customer_info', $customer_info);
            }
        }

        return $this;
    }

    /**
     * Подготвя статусите на поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderStatuses() {
        // Зареждане на модела за статуси
        $this->loadModelAs('localisation/order_status', 'orderStatuses');

        // Получаване на всички статуси
        $order_statuses = $this->orderStatuses->getOrderStatuses();

        $this->setData('order_statuses', $order_statuses);

        return $this;
    }

    /**
     * Подготвя методите за плащане
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePaymentMethods() {
        $this->loadModelAs('setting/payment', 'paymentModel');
        $payment_methods = $this->paymentModel->getPaymentMethods();
        $this->setData('payment_methods', $payment_methods);
        return $this;
    }

    /**
     * Подготвя методите за доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareShippingMethods() {
        $this->loadModelAs('setting/delivery', 'deliveryModel');

        // Получаваме всички методи с техните sub-методи
        $all_methods = $this->deliveryModel->getAllDeliveryMethodsWithSubMethods($admin_mode = true);

        // Нормализираме текущия shipping_code за правилно сравнение
        $current_shipping_code = $this->getData('shipping_code');
        $normalized_current_code = $this->deliveryModel->normalizeShippingCode($current_shipping_code);

        // Актуализираме данните с нормализирания код
        $this->setData('shipping_code', $normalized_current_code);
        $this->setData('shipping_methods', $all_methods);
        return $this;
    }

    /**
     * Подготвя динамични допълнителни полета за доставка
     */
    private function prepareShippingExtra() {
        $shipping_code = $this->getData('shipping_code');
        $shipping_extra = $this->resolveShippingExtraHtml($shipping_code);
        $this->setData('shipping_extra', $shipping_extra);
        return $this;
    }

    private function resolveShippingExtraHtml($shipping_code) {
        if (!$shipping_code) { return ''; }
        $this->loadModelAs('setting/delivery', 'deliveryModel');
        $methods = (array)$this->deliveryModel->getDeliveryMethods();
        $main = explode('.', (string)$shipping_code, 2)[0];

        foreach ($methods as $method) {
            if (empty($method['status'])) { continue; }
            $code = $method['code'] ?? '';
            if ($code !== $main) { continue; }
            $class = '\\Theme25\\Delivery\\' . ucfirst($code);
            if (class_exists($class) && is_callable([$class, 'injectAdditionalFunctionality'])) {
                try { return (string)call_user_func([$class, 'injectAdditionalFunctionality'], $shipping_code); }
                catch (\Throwable $e) { return ''; }
            }
        }
        return '';
    }

    /**
     * Зарежда списък с държави за селект менютата
     */
    private function prepareCountries() {
        $this->loadModelAs('localisation/country', 'countryModel');
        $countries = $this->countryModel->getCountries();
        $this->setData('countries', $countries);
        return $this;
    }

    /**
     * Подготвя default зони ако липсват в поръчката
     */
    private function prepareDefaultZonesIfNeeded() {
        $order_data = $this->getData();

        // Проверка дали трябва да заредим default зони
        $need_payment_zones = empty($order_data['payment_zone_id']) && !empty($order_data['payment_country_id']);
        $need_shipping_zones = empty($order_data['shipping_zone_id']) && !empty($order_data['shipping_country_id']);

        if ($need_payment_zones || $need_shipping_zones) {
            $this->loadModelAs('localisation/zone', 'zoneModel');

            if ($need_payment_zones) {
                $zones = $this->zoneModel->getZonesByCountryId($order_data['payment_country_id']);
                if (!empty($zones)) {
                    $this->setData('payment_zone_id', $zones[0]['zone_id']);
                }
            }

            if ($need_shipping_zones) {
                $zones = $this->zoneModel->getZonesByCountryId($order_data['shipping_country_id']);
                if (!empty($zones)) {
                    $this->setData('shipping_zone_id', $zones[0]['zone_id']);
                }
            }
        }

        return $this;
    }

    /**
     * Подготвя клиентските групи
     */
    private function prepareCustomerGroups() {
        $this->loadModelAs('customer/customer_group', 'customerGroupModel');
        $customer_groups = $this->customerGroupModel->getCustomerGroups();
        $this->setData('customer_groups', $customer_groups);
        return $this;
    }

    /**
     * AJAX метод за търсене на продукти
     */
    public function searchProducts() {
        $this->initAdminData();

        $search = $this->requestPost('search', '');
        $limit = (int)$this->requestPost('limit', 20);
        $start = (int)$this->requestPost('start', 0);

        $this->loadModelAs('catalog/product', 'productModel');
        $this->loadModelAs('tool/image', 'imageModel');

        $filter_data = [
            'filter_name' => $search,
            'start' => $start,
            'limit' => $limit
        ];

        $products = $this->productModel->getProducts($filter_data);
        $results = [];

        foreach ($products as $product) {
            $image = 'no_image.png';
            if (!empty($product['image'])) {
                $image = $product['image'];
            }

            $results[] = [
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'price' => number_format($product['price'], 2, '.', ''),
                'image' => $this->imageModel->resize($image, 40, 40),
                'image_large' => $this->imageModel->resize($image, 80, 80)
            ];
        }

        // Проверка дали има още продукти
        $total_products = $this->productModel->getTotalProducts($filter_data);
        $has_more = ($start + $limit) < $total_products;

        header('Content-Type: application/json');
        echo json_encode([
            'products' => $results,
            'has_more' => $has_more,
            'total' => $total_products
        ]);
        exit;
    }

    /**
     * AJAX метод за получаване на опциите на продукт
     */
    public function getProductOptions() {
        $this->initAdminData();

        $product_id = (int)$this->requestPost('product_id', 0);

        if (!$product_id) {
            header('Content-Type: application/json');
            echo json_encode(['options' => []]);
            exit;
        }

        $this->loadModelAs('catalog/product', 'productModel');

        $product_options = $this->productModel->getProductOptions($product_id);
        $options = [];

        foreach ($product_options as $product_option) {
            $option_values = [];

            // Проверка дали има стойности за опцията
            if (isset($product_option['product_option_value']) && is_array($product_option['product_option_value'])) {
                foreach ($product_option['product_option_value'] as $option_value) {
                    $option_values[] = [
                        'product_option_value_id' => $option_value['product_option_value_id'],
                        'option_value_id' => $option_value['option_value_id'],
                        'name' => trim($option_value['name']),
                        'price' => $option_value['price'],
                        'price_prefix' => $option_value['price_prefix']
                    ];
                }
            }

            $options[] = [
                'product_option_id' => $product_option['product_option_id'],
                'option_id' => $product_option['option_id'],
                'name' => $product_option['name'],
                'type' => $product_option['type'],
                'required' => $product_option['required'],
                'option_values' => $option_values
            ];
        }

        header('Content-Type: application/json');
        echo json_encode(['options' => $options]);
        exit;
    }

    /**
     * AJAX метод за прилагане на ваучер код
     */
    public function applyVoucher($order_id = 0, $voucher_code = '') {

        $flag_no_ajax = false;
        if($order_id && $voucher_code) {
            $flag_no_ajax = true;
        }
        else {
            $this->initAdminData();
        }

        $order_id = $order_id ? $order_id : (int)$this->requestPost('order_id', 0);
        $voucher_code = $voucher_code ? $voucher_code : trim($this->requestPost('voucher_code', ''));

        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } elseif (empty($voucher_code)) {
            $json['error'] = 'Моля въведете ваучер код';
        } else {
            try {
                // Опит за зареждане на модела за ваучери
                try {
                    $this->loadModelAs('extension/total/voucher', 'voucherModel');
                    $voucher_info = $this->voucherModel->getVoucher($voucher_code);
                } catch (Exception $e) {
                    // Fallback - директна проверка в базата данни
                    $voucher_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "voucher WHERE code = '" . $this->db->escape($voucher_code) . "' AND status = '1'");
                    $voucher_info = $voucher_query->num_rows ? $voucher_query->row : false;
                }

                if ($voucher_info && (isset($voucher_info['status']) ? $voucher_info['status'] : true)) {
                    // Прилагане на ваучера към поръчката
                    $this->totalModel->applyVoucherToOrder($order_id, $voucher_code, $voucher_info);

                    // Синхронизация на ваучера с OrderSession (ако е активна)
                    if ($this->order_session) {
                        $this->order_session->set('applied_voucher', [
                            'code' => $voucher_code,
                            'amount' => $voucher_info['amount'],
                            'applied_at' => date('Y-m-d H:i:s')
                        ]);

                        // Принудителна синхронизация с базата данни
                        $sync_success = $this->forceSyncOrderSession();
                        $json['sync_success'] = $sync_success;
                    }

                    // Получаване на продуктите от OrderSession (ако е активна) или от БД
                    if ($this->order_session) {
                        $products = $this->order_session->getOrderData('order_products');
                    } else {
                        // Fallback - зареждане от базата данни
                        $products_query = $this->db->query("
                            SELECT * FROM " . DB_PREFIX . "order_product
                            WHERE order_id = '" . (int)$order_id . "'
                        ");
                        $products = $products_query->rows;
                    }

                    // Изчисляване на тоталите с 'detailed' формат (като другите AJAX методи)
                    $calculated_totals = $this->totalModel->calculateTotals($products, $order_id, 'detailed');

                    // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                    if ($this->order_session) {
                        $this->order_session->updateOrderTotals($calculated_totals);
                    }

                    // Форматиране на тоталите (като другите AJAX методи)
                    $formatted_totals = $this->formatOrderTotals($calculated_totals);

                    $json['success'] = 'Ваучерът е приложен успешно';
                    $json['totals'] = $formatted_totals;
                    $json['debug'] = [
                        'voucher_amount' => $voucher_info['amount'],
                        'totals_count' => count($formatted_totals),
                        'raw_totals_count' => count($calculated_totals),
                        'products_count' => count($products)
                    ];
                } else {
                    $json['error'] = 'Невалиден или неактивен ваучер код';
                }
            } catch (Exception $e) {
                $json['error'] = 'Грешка при прилагане на ваучера: ' . $e->getMessage();
            }
        }

        if($flag_no_ajax) {
            return $json;
        } else {
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * AJAX метод за прилагане на купон код
     */
    public function applyCoupon($order_id = 0, $coupon_code = '') {

        $flag_no_ajax = false;
        if($order_id && $coupon_code) {
            $flag_no_ajax = true;
        }
        else {
            $this->initAdminData();
        }

        $order_id = $order_id ? $order_id : (int)$this->requestPost('order_id', 0);
        $coupon_code = $coupon_code ? $coupon_code : trim($this->requestPost('coupon_code', ''));

        // Получаване на актуалните продуктни данни от формата (като в recalculateOrderTotalsAjax)
        $products_data = $this->requestPost('products', []);
        $order_product_ids = $this->requestPost('order_product_ids', []);

        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } elseif (empty($coupon_code)) {
            $json['error'] = 'Моля въведете купон код';
        } else {

            try {
                // Подготовка на пълни продуктни данни (същата логика като в recalculateOrderTotalsAjax)
                $full_products_data = [];
                if (!empty($products_data) && !empty($order_product_ids)) {
                    $full_products_data = $this->prepareFullProductsDataForCalculation($order_id, $products_data, $order_product_ids);
                } else {
                    // Fallback - използваме данните от OrderSession или БД
                    if ($this->order_session && $this->order_session->isOrderDataLoaded($order_id)) {
                        $full_products_data = $this->order_session->getOrderData('order_products');
                    } else {
                        $full_products_data = $this->getFullProductsFromDatabase($order_id);
                    }
                }

                // Опит за зареждане на модела за купони
                try {
                    $this->loadModelAs('extension/total/coupon', 'couponModel');
                    $coupon_info = $this->couponModel->getCoupon($coupon_code);
                } catch (Exception $e) {
                    // Fallback - директна проверка в базата данни
                    $coupon_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "coupon WHERE code = '" . $this->db->escape($coupon_code) . "' AND status = '1'");
                    $coupon_info = $coupon_query->num_rows ? $coupon_query->row : false;
                }

                if ($coupon_info && (isset($coupon_info['status']) ? $coupon_info['status'] : true)) {
                    // Прилагане на купона към поръчката с актуалните продуктни данни
                    $this->totalModel->applyCouponToOrderWithProducts($order_id, $coupon_code, $coupon_info, $full_products_data);

                    // Синхронизация на купона с OrderSession (ако е активна)
                    if ($this->order_session) {
                        // КОРЕКЦИЯ: Записване на информация за купона в сесията
                        $this->order_session->set('applied_coupon', [
                            'code' => $coupon_code,
                            'type' => $coupon_info['type'],
                            'discount' => $coupon_info['discount'],
                            'applied_at' => date('Y-m-d H:i:s')
                        ]);

                        // Принудителна синхронизация с базата данни
                        $sync_success = $this->forceSyncOrderSession();
                        $json['sync_success'] = $sync_success;
                    }

                    $calculated_totals = $this->totalModel->calculateTotals($full_products_data, $order_id, 'detailed');

                    // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                    if ($this->order_session) {
                        $this->order_session->updateOrderTotals($calculated_totals);
                    }

                    $json['success'] = 'Купонът е приложен успешно';
                    $json['totals'] = $calculated_totals;
                } else {
                    $json['error'] = 'Невалиден или неактивен купон код';
                }
            } catch (Exception $e) {
                $json['error'] = 'Грешка при прилагане на купона: ' . $e->getMessage();
            }
        }

        if($flag_no_ajax) {
            return $json;
        } else {
            $this->setJSONResponseOutput($json);
        }
    }
    /**
     * AJAX: Премахва купон от order_total и връща преизчислени суми
     */
    public function removeCouponAjax() {
        $this->initAdminData();
        $order_id = (int)$this->requestPost('order_id', 0);
        $json = [];
        try {
            if (!$order_id) throw new \Exception('Невалиден ID на поръчка');

            // Премахване от базата данни
            $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'coupon'");

            // Синхронизация с OrderSession (ако е активна)
            if ($this->order_session) {

                $this->order_session->remove('applied_coupon');

                $totals = $this->order_session->getOrderTotals();
                if($totals) {
                    $new_totals = [];
                    foreach($totals as $total) {
                        if($total['code'] == 'coupon') {
                            continue;
                        }
                        $new_totals[] = $total;
                    }
                    $totals = $new_totals;
                }

                $this->order_session->set('order_totals', $totals);

                // Принудителна синхронизация с базата данни
                // $sync_success = $this->forceSyncOrderSession();
                // $json['sync_success'] = $sync_success;
            }

            // Получаване на продуктите от OrderSession (ако е активна) или от БД
            if ($this->order_session) {
                $products = $this->order_session->getOrderData('order_products');
            } else {
                // Fallback - зареждане от базата данни
                $products_query = $this->db->query("
                    SELECT * FROM " . DB_PREFIX . "order_product
                    WHERE order_id = '" . (int)$order_id . "'
                ");
                $products = $products_query->rows;
            }

            // Изчисляване на тоталите с 'detailed' формат (като другите AJAX методи)
            $calculated_totals = $this->totalModel->calculateTotals($products, $order_id, 'detailed');

            $this->order_session->set('order_totals', $calculated_totals);

            // Форматиране на тоталите (като другите AJAX методи)
            // $formatted_totals = $this->formatOrderTotals($calculated_totals);

            $json['success'] = true;
            $json['totals'] = $calculated_totals;
        } catch (\Exception $e) {
            $json['error'] = 'Грешка при премахване на купона: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX: Премахва ваучер от order_total и връща преизчислени суми
     */
    public function removeVoucherAjax() {
        $this->initAdminData();
        $order_id = (int)$this->requestPost('order_id', 0);
        $json = [];
        try {
            if (!$order_id) throw new \Exception('Невалиден ID на поръчка');

            // Премахване от базата данни
            $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'voucher'");

            // Синхронизация с OrderSession (ако е активна)
            if ($this->order_session) {
                $this->order_session->remove('applied_voucher');

                $totals = $this->order_session->getOrderTotals();
                if($totals) {
                    $new_totals = [];
                    foreach($totals as $total) {
                        if($total['code'] == 'voucher') {
                            continue;
                        }
                        $new_totals[] = $total;
                    }
                    $totals = $new_totals;
                }

                $this->order_session->set('order_totals', $totals);

                // Принудителна синхронизация с базата данни
                $sync_success = $this->forceSyncOrderSession();
                $json['sync_success'] = $sync_success;
            }

            // Получаване на продуктите от OrderSession (ако е активна) или от БД
            if ($this->order_session) {
                $products = $this->order_session->getOrderData('order_products');
            } else {
                // Fallback - зареждане от базата данни
                $products_query = $this->db->query("
                    SELECT * FROM " . DB_PREFIX . "order_product
                    WHERE order_id = '" . (int)$order_id . "'
                ");
                $products = $products_query->rows;
            }

            // Изчисляване на тоталите с 'detailed' формат (като другите AJAX методи)
            $calculated_totals = $this->totalModel->calculateTotals($products, $order_id, 'detailed');
            $this->order_session->set('order_totals', $calculated_totals);

            $json['success'] = true;
            $json['totals'] = $calculated_totals;
        } catch (\Exception $e) {
            $json['error'] = 'Грешка при премахване на ваучера: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }


    /**
     * AJAX метод за преизчисляване на общите суми с правилно валутно форматиране
     */
    public function recalculateOrderTotalsAjax() {
        // $this->initAdminData();

        $order_id = (int)$this->requestPost('order_id', 0);
        // Продуктите пристигат като products[index][field] във FormData
        $products_data = $this->requestPost('products', []);
        // Композитните order_product_id пристигат като order_product_ids[index]
        $order_product_ids = $this->requestPost('order_product_ids', []);

        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } else {
            try {

                // НОВА ЛОГИКА: Използваме същия подход като calculateTotalsFromSessionAjax
                // Първо получаваме всички продукти от OrderSession (включително новодобавените)
                if ($this->order_session && $this->order_session->isOrderDataLoaded($order_id)) {
                    $all_products = $this->order_session->getOrderData('order_products');

                    // Актуализираме продуктите с данните от формата
                    $updated_products = $this->updateSessionProductsWithFormData($all_products, $products_data, $order_product_ids);

                } else {
                    // Fallback към старата логика ако OrderSession не е налична
                    $updated_products = $this->prepareFullProductsDataForCalculation($order_id, $products_data, $order_product_ids);
                }

                // Изчисляване на новите суми на базата на актуализираните продуктни данни
                $calculated_totals = $this->totalModel->calculateTotals($updated_products, $order_id, 'detailed');

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                if ($this->order_session && $this->order_session->isOrderDataLoaded($order_id)) {
                    $this->order_session->updateOrderTotals($calculated_totals);
                }

                $json['success'] = true;
                $json['totals'] = $calculated_totals;

            } catch (\Exception $e) {
                $json['error'] = 'Грешка при преизчисляване на сумите: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Актуализира продуктите от OrderSession с данните от формата
     * Този метод гарантира, че всички продукти (включително новодобавените) се включват в изчислението
     *
     * @param array $session_products Всички продукти от OrderSession
     * @param array $form_products_data Данни от формата
     * @param array $form_product_ids Композитни ID-та от формата
     * @return array Актуализирани продукти
     */
    private function updateSessionProductsWithFormData($session_products, $form_products_data, $form_product_ids) {
        // Създаваме индекс на данните от формата по композитен ID
        $form_data_by_id = [];
        foreach ($form_products_data as $index => $form_product) {
            if (isset($form_product_ids[$index])) {
                $composite_id = $form_product_ids[$index];
                $form_data_by_id[$composite_id] = $form_product;
            }
        }

        $updated_products = [];

        // Обхождаме всички продукти от сесията
        foreach ($session_products as $session_product) {
            // Генерираме композитен ID за продукта от сесията за съпоставяне с формата
            $session_composite_id = $this->generateCompositeIdForProduct($session_product);

            // Ако имаме данни от формата за този продукт (по композитен ID), актуализираме ги
            if (isset($form_data_by_id[$session_composite_id])) {
                $form_data = $form_data_by_id[$session_composite_id];

                // Актуализираме количество и цена
                if (isset($form_data['quantity'])) {
                    $session_product['quantity'] = (int)$form_data['quantity'];
                }
                if (isset($form_data['price'])) {
                    $session_product['price'] = (float)$form_data['price'];
                }

                // Преизчисляваме total
                $session_product['total'] = $session_product['quantity'] * $session_product['price'];
            }
            // Продуктите без данни от формата (новодобавени) се запазват както са

            $updated_products[] = $session_product;
        }

        return $updated_products;
    }

    /**
     * Подготвя пълни продуктни данни за изчисление на тотали
     * Използва същия формат като в updateProductQuantityInSessionAjax
     *
     * @param int $order_id ID на поръчката
     * @param array $partial_products_data Частични данни от JavaScript формата
     * @param array $order_product_ids Композитни order_product_id
     * @return array Пълни продуктни данни с всички полета
     */
    private function prepareFullProductsDataForCalculation($order_id, $partial_products_data, $order_product_ids) {
        error_log("=== DEBUG prepareFullProductsDataForCalculation ===");
        error_log("Order ID: $order_id, Partial count: " . count($partial_products_data) . ", IDs count: " . count($order_product_ids));

        // Приоритет 1: Опитваме се да получим пълните данни от OrderSession
        if ($this->order_session && $this->order_session->isOrderDataLoaded($order_id)) {
            $full_products = $this->order_session->getOrderData('order_products');
            error_log("OrderSession loaded, products count: " . count($full_products));

            if (!empty($full_products)) {
                error_log("Using OrderSession data");
                // Актуализираме пълните данни с новите стойности от формата
                return $this->updateFullProductsWithFormData($full_products, $partial_products_data, $order_product_ids);
            }
        }

        // Приоритет 2: Зареждаме пълните данни от базата данни
        error_log("Loading products from database");
        $full_products = $this->getFullProductsFromDatabase($order_id);
        error_log("Database products count: " . count($full_products));

        // Актуализираме пълните данни с новите стойности от формата
        return $this->updateFullProductsWithFormData($full_products, $partial_products_data, $order_product_ids);
    }

    /**
     * Зарежда пълни продуктни данни от базата данни
     *
     * @param int $order_id ID на поръчката
     * @return array Пълни продуктни данни
     */
    private function getFullProductsFromDatabase($order_id) {
        $products = [];

        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_product
            WHERE order_id = '" . (int)$order_id . "'
            ORDER BY order_product_id
        ");

        foreach ($query->rows as $product) {
            // Зареждане на опциите за всеки продукт
            $options_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_option
                WHERE order_id = '" . (int)$order_id . "'
                AND order_product_id = '" . (int)$product['order_product_id'] . "'
            ");

            $product['options'] = $options_query->rows;
            $products[] = $product;
        }

        return $products;
    }
    /**
     * Актуализира пълните продуктни данни с новите стойности от формата
     *
     * @param array $full_products Пълни продуктни данни от OrderSession или БД
     * @param array $partial_products_data Частични данни от JavaScript формата
     * @param array $order_product_ids Композитни order_product_id
     * @return array Актуализирани пълни продуктни данни
     */
    private function updateFullProductsWithFormData($full_products, $partial_products_data, $order_product_ids) {
        $updated_products = [];

        // DEBUG: Логиране на входните данни
        error_log("=== DEBUG updateFullProductsWithFormData ===");
        error_log("Full: " . count($full_products) . ", Partial: " . count($partial_products_data) . ", IDs: " . count($order_product_ids));

        // Създаваме индекс на частичните данни по композитен ID за бърз достъп
        $partial_data_by_composite_id = [];
        foreach ($partial_products_data as $index => $partial_product) {
            if (isset($order_product_ids[$index])) {
                $composite_id = $order_product_ids[$index];
                $partial_data_by_composite_id[$composite_id] = $partial_product;
                error_log("Mapped: '$composite_id' -> qty:{$partial_product['quantity']}, price:{$partial_product['price']}");
            }
        }

        // Създаваме индекс на пълните продукти по композитен ID
        $full_products_by_composite_id = [];
        foreach ($full_products as $full_product) {
            $composite_id = $this->generateCompositeIdForProduct($full_product);
            $full_products_by_composite_id[$composite_id] = $full_product;
        }

        // Обхождаме всички частични данни от формата (това гарантира, че всички продукти се обработват)
        foreach ($partial_data_by_composite_id as $composite_id => $partial_data) {
            error_log("Processing composite ID: '$composite_id'");

            // Ако имаме пълни данни за този продукт, използваме ги
            if (isset($full_products_by_composite_id[$composite_id])) {
                $full_product = $full_products_by_composite_id[$composite_id];
                error_log("MATCH found - using full product data for '$composite_id'");

                // Актуализираме quantity и price
                if (isset($partial_data['quantity'])) {
                    $full_product['quantity'] = (int)$partial_data['quantity'];
                }

                if (isset($partial_data['price'])) {
                    $full_product['price'] = (float)$partial_data['price'];
                }

                // Преизчисляваме total
                $full_product['total'] = $full_product['quantity'] * $full_product['price'];
                error_log("Updated '$composite_id': qty={$full_product['quantity']}, price={$full_product['price']}, total={$full_product['total']}");

                $updated_products[] = $full_product;
            } else {
                // Ако няма пълни данни, създаваме минимален продуктен запис от частичните данни
                error_log("NO MATCH - creating minimal product record for '$composite_id'");

                $minimal_product = $this->createMinimalProductFromPartialData($composite_id, $partial_data);
                if ($minimal_product) {
                    error_log("Created minimal product for '$composite_id': qty={$minimal_product['quantity']}, price={$minimal_product['price']}, total={$minimal_product['total']}");
                    $updated_products[] = $minimal_product;
                } else {
                    error_log("FAILED to create minimal product for '$composite_id'");
                }
            }
        }

        error_log("Final updated products count: " . count($updated_products));
        error_log("=== END DEBUG updateFullProductsWithFormData ===");

        return $updated_products;
    }
    /**
     * Създава минимален продуктен запис от частични данни
     * Използва се когато продукт липсва в OrderSession/БД, но се изпраща от формата
     *
     * @param string $composite_id Композитен ID на продукта
     * @param array $partial_data Частични данни от формата
     * @return array|null Минимален продуктен запис или null при грешка
     */
    private function createMinimalProductFromPartialData($composite_id, $partial_data) {
        // Извличаме product_id от композитния ID
        $product_id = $this->extractProductIdFromCompositeId($composite_id);

        if (!$product_id) {
            error_log("Cannot extract product_id from composite ID: '$composite_id'");
            return null;
        }

        // Зареждаме основна информация за продукта от базата данни
        $product_info = $this->getBasicProductInfo($product_id);

        if (!$product_info) {
            error_log("Cannot load product info for product_id: $product_id");
            return null;
        }

        // Създаваме минимален продуктен запис
        $minimal_product = [
            'order_product_id' => $composite_id,
            'order_id' => 0, // Ще се попълни при запазване
            'product_id' => $product_id,
            'name' => $product_info['name'],
            'model' => $product_info['model'],
            'quantity' => isset($partial_data['quantity']) ? (int)$partial_data['quantity'] : 1,
            'price' => isset($partial_data['price']) ? (float)$partial_data['price'] : 0.00,
            'custom_price' => 0.00,
            'custom_total' => 0.00,
            'tax' => 0.0000,
            'reward' => 0,
            'options' => [] // Ще се попълни ако е необходимо
        ];

        // Изчисляваме total
        $minimal_product['total'] = $minimal_product['quantity'] * $minimal_product['price'];

        return $minimal_product;
    }

    /**
     * Извлича product_id от композитен ID
     *
     * @param string $composite_id Композитен ID (напр. "5010301_4956_17115")
     * @return int|null Product ID или null при грешка
     */
    private function extractProductIdFromCompositeId($composite_id) {
        $parts = explode('_', $composite_id);

        if (empty($parts[0]) || !is_numeric($parts[0])) {
            return null;
        }

        return (int)$parts[0];
    }

    /**
     * Зарежда основна информация за продукт от правилната база данни
     * Използва модела sale/order за правилна работа с базите данни
     *
     * @param int $product_id ID на продукта
     * @return array|null Основна информация за продукта или null при грешка
     */
    private function getBasicProductInfo($product_id) {
        // Зареждаме модела ако не е зареден
        if (!isset($this->orders)) {
            $this->loadModelAs('sale/order', 'orders');
        }

        // Използваме новия метод от модела
        return $this->orders->getProductInfo($product_id);
    }

    /**
     * Генерира композитен ID за продукт на базата на неговите данни
     *
     * @param array $product Продуктни данни
     * @return string Композитен ID
     */
    private function generateCompositeIdForProduct($product) {
        if ($this->order_session) {
            $options = isset($product['options']) ? $product['options'] : [];
            return $this->order_session->generateCompositeProductId($product['product_id'], $options);
        }

        // Fallback към реалния order_product_id ако няма OrderSession
        return $product['order_product_id'];
    }





    /**
     * Изчислява сумата на "други тотали" (всички освен основните)
     */
    private function calculateOtherTotals($total_sums) {
        $other_totals = 0;
        $main_totals = ['sub_total', 'total', 'shipping', 'tax', 'voucher', 'coupon'];

        foreach ($total_sums as $code => $value) {
            if (!in_array($code, $main_totals)) {
                $other_totals += $value;
            }
        }

        return $other_totals;
    }

    private function reArrangeTotals($total_sums) {
        $new_total_sums = [];
        $main_totals = ['sub_total', 'shipping', 'tax', 'voucher', 'coupon'];

        foreach ($main_totals as $code) {
            if (isset($total_sums[$code])) {
                $new_total_sums[$code] = $total_sums[$code];
            }
        }

        foreach ($total_sums as $code => $value) {
            if (!in_array($code, $main_totals)) {
                $new_total_sums[$code] = $value;
            }
        }

        $new_total_sums['total'] = $total_sums['total'];

        return $new_total_sums;
    }

    /**
     * Форматира общите суми с правилната валута
     */
    private function formatOrderTotals($calculated_totals) {
        // Проверка дали получаваме новия детайлен формат или стария прост формат
        $is_detailed_format = isset($calculated_totals['sub_total']['value']);

        if ($is_detailed_format) {
            // Новия детайлен формат вече съдържа форматирани данни
            $formatted_totals = [];

            foreach ($calculated_totals as $code => $total_data) {
                // Показваме всички тотали (включително нулевите за пълна информация)
                // Изключение: скриваме само тотали с нулева стойност които не са основни
                $is_main_total = in_array($code, ['sub_total', 'shipping', 'tax', 'total']);
                $has_value = $total_data['value'] != 0;

                if ($is_main_total || $has_value) {
                    $formatted_totals[] = [
                        'code' => $total_data['code'],
                        'title' => $total_data['title'],
                        'text' => $total_data['text'],
                        'value' => $total_data['value']
                    ];
                }
            }

            return $formatted_totals;
        }

        // Стария формат - запазваме съществуващата логика за обратна съвместимост
        $currency_code = $this->getData('currency_code');
        $currency_value = $this->getData('currency_value');

        $formatted_totals = [];

        // Междинна сума
        if (isset($calculated_totals['sub_total']) && $calculated_totals['sub_total'] > 0) {
            $formatted_totals[] = [
                'code' => 'sub_total',
                'title' => 'Цена на продуктите',
                'text' => $this->formatCurrency($calculated_totals['sub_total'], $currency_code, $currency_value),
                'value' => $calculated_totals['sub_total']
            ];
        }

        // Доставка
        if (isset($calculated_totals['shipping']) && $calculated_totals['shipping'] != 0) {
            $formatted_totals[] = [
                'code' => 'shipping',
                'title' => 'Безплатна доставка',
                'text' => $this->formatCurrency($calculated_totals['shipping'], $currency_code, $currency_value),
                'value' => $calculated_totals['shipping']
            ];
        }

        // Данък
        if (isset($calculated_totals['tax']) && $calculated_totals['tax'] != 0) {
            $formatted_totals[] = [
                'code' => 'tax',
                'title' => 'Данък',
                'text' => $this->formatCurrency($calculated_totals['tax'], $currency_code, $currency_value),
                'value' => $calculated_totals['tax']
            ];
        }

        // Други суми
        if (isset($calculated_totals['other_totals']) && $calculated_totals['other_totals'] != 0) {
            $formatted_totals[] = [
                'code' => 'other',
                'title' => 'Други такси',
                'text' => $this->formatCurrency($calculated_totals['other_totals'], $currency_code, $currency_value),
                'value' => $calculated_totals['other_totals']
            ];
        }

        // Ваучер отстъпка
        if (isset($calculated_totals['voucher']) && $calculated_totals['voucher'] != 0) {
            $formatted_totals[] = [
                'code' => 'voucher',
                'title' => 'Код за отстъпка (ваучер)',
                'text' => $this->formatCurrency($calculated_totals['voucher'], $currency_code, $currency_value),
                'value' => $calculated_totals['voucher']
            ];
        }

        // Купон отстъпка
        if (isset($calculated_totals['coupon']) && $calculated_totals['coupon'] != 0) {
            $formatted_totals[] = [
                'code' => 'coupon',
                'title' => 'Код за отстъпка (купон)',
                'text' => $this->formatCurrency($calculated_totals['coupon'], $currency_code, $currency_value),
                'value' => $calculated_totals['coupon']
            ];
        }

        // Обща сума
        if (isset($calculated_totals['total'])) {
            $formatted_totals[] = [
                'code' => 'total',
                'title' => 'Общо',
                'text' => $this->formatCurrency($calculated_totals['total'], $currency_code, $currency_value),
                'value' => $calculated_totals['total']
            ];
        }

        return $formatted_totals;
    }

    /**
     * Принудителна синхронизация на OrderSession данните с базата данни
     *
     * @return bool Успешна ли е синхронизацията
     */
    private function forceSyncOrderSession() {
        if (!$this->order_session) {
            return false;
        }

        try {
            // Принуждаваме записване чрез промяна на is_dirty флага
            $reflection = new \ReflectionClass($this->order_session);
            $is_dirty_property = $reflection->getProperty('is_dirty');
            $is_dirty_property->setAccessible(true);
            $is_dirty_property->setValue($this->order_session, true);

            // Извикваме saveSessionData метода
            $save_method = $reflection->getMethod('saveSessionData');
            $save_method->setAccessible(true);
            $save_method->invoke($this->order_session);

            return true;
        } catch (\Exception $e) {
            error_log("OrderSession sync error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Запазва временни данни за редактиране в OrderSession
     */
    public function saveEditingData($key, $data) {
        if ($this->order_session) {
            $this->order_session->set('editing_data.' . $key, $data);
            return true;
        }
        return false;
    }

    /**
     * Получава временни данни за редактиране от OrderSession
     */
    public function getEditingData($key, $default = null) {
        if ($this->order_session) {
            return $this->order_session->get('editing_data.' . $key, $default);
        }
        return $default;
    }

    /**
     * Изчиства временните данни за редактиране от OrderSession
     */
    public function clearEditingData($key = null) {
        if ($this->order_session) {
            if ($key) {
                $this->order_session->remove('editing_data.' . $key);
            } else {
                $this->order_session->remove('editing_data');
            }
            return true;
        }
        return false;
    }

    /**
     * Получава информация за текущата OrderSession
     */
    public function getOrderSessionInfo() {
        if ($this->order_session) {
            return [
                'session_key' => $this->order_session->getSessionKey(),
                'session_data' => $this->order_session->getAllData(),
                'order_id' => $this->order_session->get('session_info.order_id')
            ];
        }
        return null;
    }

    /**
     * AJAX метод за получаване на информация за OrderSession (за debug)
     */
    public function getOrderSessionInfoAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $session_info = $this->getOrderSessionInfo();

            if ($session_info) {
                $json['success'] = true;
                $json['session_info'] = $session_info;
                $json['message'] = 'OrderSession е активна и функционира правилно';
            } else {
                $json['error'] = 'OrderSession не е инициализирана';
            }
        } catch (Exception $e) {
            $json['error'] = 'Грешка при получаване на OrderSession информация: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за добавяне на продукт в OrderSession
     */
    public function addProductToSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);
            $product_id = (int)$this->requestPost('product_id', 0);
            $quantity = (int)$this->requestPost('quantity', 1);
            $price = (float)$this->requestPost('price', 0);
            $name = $this->requestPost('name', '');
            $model = $this->requestPost('model', '');
            $options = $this->requestPost('options', []);

            if (!$order_id || !$product_id) {
                throw new \Exception('Невалидни данни за продукт');
            }

            // Зареждане на информация за продукта
            $this->loadModelAs('catalog/product', 'productModel');
            $product_info = $this->productModel->getProduct($product_id);

            if (!$product_info) {
                throw new \Exception('Продуктът не е намерен');
            }

            // Обработка на опциите
            $processed_options = [];
            if (is_array($options)) {
                foreach ($options as $option) {
                    if (is_array($option) && !empty($option['name']) && !empty($option['value'])) {
                        $processed_options[] = [
                            'product_option_id' => $option['product_option_id'] ?? 0,
                            'product_option_value_id' => $option['product_option_value_id'] ?? 0,
                            'option_value_id' => $option['option_value_id'] ?? 0,
                            'name' => trim($option['name']),
                            'value' => trim($option['value'])
                        ];
                    }
                }
            }

            // Подготовка на данните за продукта
            $product_data = [
                'product_id' => $product_id,
                'name' => !empty($name) ? $name : $product_info['name'],
                'model' => !empty($model) ? $model : $product_info['model'],
                'quantity' => $quantity,
                'price' => $price,
                'total' => $price * $quantity,
                'tax' => 0, // Ще се изчисли при нужда
                'reward' => 0,
                'options' => $processed_options
            ];

            // Добавяне в OrderSession
            if ($this->order_session) {
                $this->order_session->addOrderProduct($product_data);

                // Принудителна синхронизация с базата данни
                $sync_success = $this->forceSyncOrderSession();

                // Получаване на актуализираните продукти
                $updated_products = $this->order_session->getOrderData('order_products');

                // Изчисляване на новите тотали
                $calculated_totals = $this->totalModel->calculateTotals($updated_products, $order_id, 'detailed');

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                $this->order_session->updateOrderTotals($calculated_totals);

                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                $json['success'] = true;
                $json['message'] = 'Продуктът е добавен временно';
                $json['products_count'] = count($updated_products);
                $json['product_data'] = $product_data;
                $json['totals'] = $formatted_totals;
                $json['sync_success'] = $sync_success;
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при добавяне на продукт: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за премахване на продукт от OrderSession (временно)
     */
    public function removeProductFromSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);
            $order_product_id = $this->requestPost('order_product_id', '');

            if (!$order_id || empty($order_product_id)) {
                throw new \Exception('Невалидни данни');
            }

            // Премахване от OrderSession
            if ($this->order_session) {
                $this->order_session->removeOrderProduct($order_product_id);

                // Принудителна синхронизация с базата данни
                $sync_success = $this->forceSyncOrderSession();

                // Получаване на актуализираните продукти
                $updated_products = $this->order_session->getOrderData('order_products');

                // Изчисляване на новите тотали
                $calculated_totals = $this->totalModel->calculateTotals($updated_products, $order_id, 'detailed');

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                $this->order_session->updateOrderTotals($calculated_totals);

                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                $json['success'] = true;
                $json['message'] = 'Продуктът е премахнат временно';
                $json['products_count'] = count($updated_products);
                $json['totals'] = $formatted_totals;
                $json['sync_success'] = $sync_success;
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при премахване на продукт: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за актуализиране на количество в OrderSession (временно)
     */
    public function updateProductQuantityInSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);
            $order_product_id = $this->requestPost('order_product_id', '');
            $quantity = (int)$this->requestPost('quantity', 1);

            if (!$order_id || empty($order_product_id) || $quantity < 0) {
                throw new \Exception('Невалидни данни');
            }

            // Актуализиране в OrderSession
            if ($this->order_session) {
                $this->order_session->updateProductQuantity($order_product_id, $quantity);

                // Принудителна синхронизация с базата данни
                $sync_success = $this->forceSyncOrderSession();

                // Получаване на актуализираните продукти
                $updated_products = $this->order_session->getOrderData('order_products');

                // Изчисляване на новите тотали
                $calculated_totals = $this->totalModel->calculateTotals($updated_products, $order_id, 'detailed');

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                $this->order_session->updateOrderTotals($calculated_totals);

                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                $json['success'] = true;
                $json['message'] = 'Количеството е актуализирано временно';
                $json['products_count'] = count($updated_products);
                $json['totals'] = $formatted_totals;
                $json['sync_success'] = $sync_success;
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при актуализиране на количество: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за актуализиране на цена в OrderSession (временно)
     */
    public function updateProductPriceInSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);
            $order_product_id = $this->requestPost('order_product_id', '');
            $price = (float)$this->requestPost('price', 0);

            if (!$order_id || empty($order_product_id) || $price < 0) {
                throw new \Exception('Невалидни данни');
            }

            // Актуализиране в OrderSession
            if ($this->order_session) {
                $this->order_session->updateProductPrice($order_product_id, $price);

                // Принудителна синхронизация с базата данни
                $sync_success = $this->forceSyncOrderSession();

                // Получаване на актуализираните продукти
                $updated_products = $this->order_session->getOrderData('order_products');

                // Изчисляване на новите тотали
                $calculated_totals = $this->totalModel->calculateTotals($updated_products, $order_id, 'detailed');

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                $this->order_session->updateOrderTotals($calculated_totals);

                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                $json['success'] = true;
                $json['message'] = 'Цената е актуализирана временно';
                $json['products_count'] = count($updated_products);
                $json['totals'] = $formatted_totals;
                $json['sync_success'] = $sync_success;
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при актуализиране на цена: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за получаване на продуктите от OrderSession
     */
    public function getProductsFromSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);

            if (!$order_id) {
                throw new \Exception('Невалиден order_id');
            }

            if ($this->order_session) {
                $products = $this->order_session->getOrderData('order_products');

                $json['success'] = true;
                $json['products'] = $products;
                $json['products_count'] = count($products);
                $json['data_loaded_at'] = $this->order_session->get('data_loaded_at');
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при получаване на продукти: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за изчисляване на тотали на базата на данните от OrderSession
     */
    public function calculateTotalsFromSessionAjax() {
        $this->initAdminData();

        $json = [];

        try {
            $order_id = (int)$this->requestPost('order_id', 0);

            if (!$order_id) {
                throw new \Exception('Невалиден order_id');
            }

            if ($this->order_session) {
                // Проверка дали OrderSession има заредени данни
                if (!$this->order_session->isOrderDataLoaded($order_id)) {
                    throw new \Exception('OrderSession няма заредени данни за поръчка #' . $order_id);
                }

                // Получаване на продуктите от сесията
                $products = $this->order_session->getOrderData('order_products');

                // Валидация на продуктите
                if (!is_array($products)) {
                    throw new \Exception('Невалидни данни за продукти в OrderSession');
                }

                // Изчисляване на тоталите с новите данни (използваме оптимизирания метод)
                $calculated_totals = $this->totalModel->calculateTotals($products, $order_id, 'detailed');

                // Валидация на изчислените тотали
                if (!is_array($calculated_totals) || empty($calculated_totals)) {
                    throw new \Exception('Грешка при изчисляване на тотали - празен резултат');
                }

                // КОРЕКЦИЯ: Обновяване на тоталите в сесията с пълната информация
                $this->order_session->updateOrderTotals($calculated_totals);

                // Форматиране на тоталите
                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                // Валидация на форматираните тотали
                if (!is_array($formatted_totals) || empty($formatted_totals)) {
                    throw new \Exception('Грешка при форматиране на тотали');
                }

                $json['success'] = true;
                $json['totals'] = $formatted_totals;
                $json['products_count'] = count($products);
                $json['calculated_at'] = date('Y-m-d H:i:s');
                $json['debug'] = [
                    'raw_totals_count' => count($calculated_totals),
                    'formatted_totals_count' => count($formatted_totals),
                    'has_sub_total' => isset($calculated_totals['sub_total']),
                    'has_total' => isset($calculated_totals['total'])
                ];
            } else {
                throw new \Exception('OrderSession не е инициализирана');
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при изчисляване на тотали: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

}


