<?php

namespace Theme25\Frontend\Controller\Common;

class Home extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/home');
    }

	public function index() {
		// Проверка за административен достъп - временно ограничение
		$this->checkAdminAccess();

		$this->setTitle('Начало - ' . $this->getConfig('config_name'));


		$this->renderTemplateWithDataAndOutput('common/home');
	}

	
}
