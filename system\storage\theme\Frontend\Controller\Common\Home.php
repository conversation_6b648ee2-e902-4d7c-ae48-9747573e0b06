<?php

namespace Theme25\Frontend\Controller\Common;

class Home extends \Theme25\Controller {

    public function __construct($registry) {

		F()->log('>>> Home controller 1', __FILE__, __LINE__);

        parent::__construct($registry, 'common/home');

		F()->log('>>> Home controller loaded', __FILE__, __LINE__);
    }

	public function index() {
		$this->setTitle('Начало - ' . $this->getConfig('config_name'));


		$this->renderTemplateWithDataAndOutput('common/home');
	}

	
}
