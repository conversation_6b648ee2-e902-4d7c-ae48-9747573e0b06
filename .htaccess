Header set x-xss-protection "1; mode=block"
Header set x-content-type-options "nosniff"
Header set strict-transport-security: "max-age=31536000; includeSubdomains; preload"
Header set x-frame-options: "SAMEORIGIN"

Header add Content-Security-Policy "default-src *; style-src * 'unsafe-inline'; script-src * 'unsafe-inline' 'unsafe-eval'; img-src * data: 'unsafe-inline'; connect-src * 'unsafe-inline'; frame-src *;"

RewriteEngine On
RewriteCond %{HTTP_USER_AGENT} ^(.*)MegaIndex.ru [NC,OR]
RewriteCond %{HTTP_USER_AGENT} ^(.*)MJ12bot [NC,OR]
RewriteCond %{HTTP_USER_AGENT} ^(.*)AhrefsBot [NC]
RewriteRule .* - [F]


#RewriteCond %{HTTP_USER_AGENT} (Linguee|Yandex|MegaIndex|BLEXBot|SemrushBot|Mail|AhrefsBot|OpenLinkProfiler|Ltx71|Rogerbot|Baidu|HubSpot|Quant|Seoscanners|TurnitinBot|Reanimator|Seznam|Magebee|Vagabondo|Dotbot|coccocbot) [NC]
#RewriteRule (.*) - [F,L]





# Prevent Direct Access to files
<FilesMatch "\.(tpl|ini|log)">
 Order deny,allow
 Deny from all
</FilesMatch>


#Redirect to htts version
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]


Options +FollowSymlinks

# Prevent Directoy listing
Options -Indexes

# Prevent Direct Access to files
<FilesMatch "(?i)((\.tpl|.twig|\.ini|(?<!robots)\.txt))">
 Require all denied
## For apache 2.2 and older, replace "Require all denied" with these two lines :
# Order deny,allow
# Deny from all
</FilesMatch>

#Order Deny,Allow
#Deny from all
#Allow from **************


# Development Logs - позволява достъп до dev.* URL адреси само от определени IP адреси
<Files ~ "^dev\.(.*)$">
 Order Allow,Deny
 Allow from **************
</Files>


RewriteEngine On
RewriteBase /

# rewrite url starts with dev. to system/dev_route.php?type=
RewriteCond %{REQUEST_URI} ^/dev\. [NC]
RewriteRule ^dev\.([^/]+)$ system/dev_route.php?type=$1 [QSA,NC,L]

# Правило за пренасочване на /dev_styles към dev_route.php
RewriteRule ^dev_styles/(.*)$ system/dev_route.php?type=styles&file=$1 [QSA,NC,L]

# Правило за пренасочване на /backend_css към admin/index.php?route=assets/css
# QSA флагът запазва всички query параметри, включително user_token
RewriteRule ^backend_css/(.*)\.css$ admin/index.php?route=assets/css&file=$1.css [QSA,NC,L]

# Правило за пренасочване на /frontend_css към index.php?route=assets/css
# QSA флагът запазва всички query параметри, включително user_token
RewriteRule ^frontend_css/(.*)\.css$ index.php?route=assets/css&file=$1.css [QSA,NC,L]

# Правило за пренасочване на /backend_js към admin/index.php?route=assets/js
# QSA флагът запазва всички query параметри, включително user_token
RewriteRule ^backend_js/(.*)\.js$ admin/index.php?route=assets/js&file=$1.js [QSA,NC,L]

# Правило за пренасочване на /frontend_js към index.php?route=assets/js
RewriteRule ^frontend_js/(.*)\.js$ index.php?route=assets/js&file=$1.js [QSA,NC,L]

# SEO URL Settings
RewriteEngine On

RewriteBase /
RewriteRule ^sitemap.xml$ index.php?route=extension/feed/google_sitemap [L]
RewriteRule ^googlebase.xml$ index.php?route=extension/feed/google_base [L]
RewriteRule ^system/storage/(.*) index.php?route=error/not_found [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !.*\.(ico|gif|jpg|jpeg|png|js|css)
RewriteRule ^([^?]*) index.php?_route_=$1 [L,QSA]

### Additional Settings that may need to be enabled for some servers
### Uncomment the commands by removing the # sign in front of it.
### If you get an "Internal Server Error 500" after enabling any of the following settings, restore the # as this means your host doesn't allow that.

# 1. If your cart only allows you to add one item at a time, it is possible register_globals is on. This may work to disable it:
# php_flag register_globals off

# 2. If your cart has magic quotes enabled, This may work to disable it:
# php_flag magic_quotes_gpc Off

# 3. Set max upload file size. Most hosts will limit this and not allow it to be overridden but you can try
# php_value upload_max_filesize 999M

# 4. set max post size. uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields
# php_value post_max_size 999M

# 5. set max time script can take. uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields
# php_value max_execution_time 200

# 6. set max time for input to be recieved. Uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields
# php_value max_input_time 200

# 7. disable open_basedir limitations
# php_admin_value open_basedir none

#<IfModule mime_module>
#  AddHandler application/x-httpd-ea-php74 .php .php7 .phtml
#</IfModule>

<IfModule mod_fcgid.c>
AddHandler fcgid-script .php
FcgidWrapper /home/<USER>/theme25/php.fcgi .php
</IfModule>

#Gzip
<ifmodule mod_deflate.c>
AddOutputFilterByType DEFLATE text/text text/html text/plain text/xml text/css application/x-javascript application/javascript text/javascript
</ifmodule>
#End Gzip

<ifModule mod_gzip.c>
mod_gzip_on Yes
mod_gzip_dechunk Yes
mod_gzip_item_include file .(html?|txt|css|js|php|pl)$
mod_gzip_item_include handler ^cgi-script$
mod_gzip_item_include mime ^text/.*
mod_gzip_item_include mime ^application/x-javascript.*
mod_gzip_item_exclude mime ^image/.*
mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</ifModule>

# BEGIN EXPIRES
<IfModule mod_expires.c>
    ExpiresActive On
#    ExpiresDefault "access plus 10 days"
#    ExpiresByType text/css "access plus 1 week"
#    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-icon "access plus 1 year"
</IfModule>
# END EXPIRES

# BEGIN Browser Caching/Headers
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/x-javascript .js
    AddType text/richtext .rtf .rtx
    AddType image/svg+xml .svg .svgz
    AddType text/plain .txt
    AddType text/xsd .xsd
    AddType text/xsl .xsl
    AddType video/asf .asf .asx .wax .wmv .wmx
    AddType video/avi .avi
    AddType image/bmp .bmp
    AddType application/java .class
    AddType video/divx .divx
    AddType application/msword .doc .docx
    AddType application/x-msdownload .exe
    AddType image/gif .gif
    AddType application/x-gzip .gz .gzip
    AddType image/x-icon .ico
    AddType image/jpeg .jpg .jpeg .jpe
    AddType application/vnd.ms-access .mdb
    AddType audio/midi .mid .midi
    AddType video/quicktime .mov .qt
    AddType audio/mpeg .mp3 .m4a
    AddType video/mp4 .mp4 .m4v
    AddType video/mpeg .mpeg .mpg .mpe
    AddType application/vnd.ms-project .mpp
    AddType application/vnd.oasis.opendocument.database .odb
    AddType application/vnd.oasis.opendocument.chart .odc
    AddType application/vnd.oasis.opendocument.formula .odf
    AddType application/vnd.oasis.opendocument.graphics .odg
    AddType application/vnd.oasis.opendocument.presentation .odp
    AddType application/vnd.oasis.opendocument.spreadsheet .ods
    AddType application/vnd.oasis.opendocument.text .odt
    AddType audio/ogg .ogg
    AddType application/pdf .pdf
    AddType image/png .png
    AddType application/vnd.ms-powerpoint .pot .pps .ppt .pptx
    AddType audio/x-realaudio .ra .ram
    AddType application/x-shockwave-flash .swf
    AddType application/x-tar .tar
    AddType image/tiff .tif .tiff
    AddType audio/wav .wav
    AddType audio/wma .wma
    AddType application/vnd.ms-write .wri
    AddType application/vnd.ms-excel .xla .xls .xlsx .xlt .xlw
    AddType application/zip .zip
</IfModule>