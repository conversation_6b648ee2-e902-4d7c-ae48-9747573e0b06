<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6e41b4',
                        secondary: '#f8f9fa',
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '6px',
                        'md': '12px',
                        'lg': '18px',
                        'xl': '24px',
                        '2xl': '30px',
                        '3xl': '36px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
            <!-- Лого на сайта (ако има) -->
            {% if site_logo %}
            <div class="mb-6">
                <img src="{{ site_logo }}" alt="{{ site_name }}" class="h-16 mx-auto">
            </div>
            {% endif %}

            <!-- Икона за разработка/профилактика -->
            <div class="mb-6">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Заглавие -->
            <h1 class="text-2xl font-bold text-gray-900 mb-4">
                {{ heading_title }}
            </h1>

            <!-- Съобщение -->
            <p class="text-gray-600 mb-6 leading-relaxed">
                {{ text_message }}
            </p>

            <!-- Информация за административния панел -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <p class="text-sm text-gray-700 mb-3">
                    {{ text_admin_login }}
                </p>
                <a href="{{ admin_login_url }}"
                   class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    {{ text_admin_login_link }}
                </a>
            </div>

            <!-- Допълнителна информация -->
            <div class="text-xs text-gray-500 border-t pt-4">
                <p>Благодарим за разбирането. Работим усилено за подобряване на услугите ни.</p>
                <p class="mt-1">HTTP Status: 503 Service Unavailable</p>
            </div>
        </div>

        <!-- Footer информация -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-500">
                За въпроси и информация, моля свържете се с нас.
            </p>
            {% if site_name %}
            <p class="text-xs text-gray-400 mt-2">
                © {{ site_name }} - Временна профилактика
            </p>
            {% endif %}
        </div>
    </div>

    <script>
        // Автоматично пренасочване към admin панела след 30 секунди (опционално)
        // setTimeout(function() {
        //     window.location.href = '{{ admin_login_url }}';
        // }, 30000);
    </script>
</body>
</html>
