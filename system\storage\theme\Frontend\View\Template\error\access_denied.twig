<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6e41b4',
                        secondary: '#f8f9fa',
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '6px',
                        'md': '12px',
                        'lg': '18px',
                        'xl': '24px',
                        '2xl': '30px',
                        '3xl': '36px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
            <!-- Икона за заключен достъп -->
            <div class="mb-6">
                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                    <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
            </div>

            <!-- Заглавие -->
            <h1 class="text-2xl font-bold text-gray-900 mb-4">
                {{ heading_title }}
            </h1>

            <!-- Съобщение -->
            <p class="text-gray-600 mb-6 leading-relaxed">
                {{ text_message }}
            </p>

            <!-- Информация за административния панел -->
            <div class="bg-blue-50 rounded-lg p-4 mb-6">
                <p class="text-sm text-blue-800 mb-3">
                    {{ text_admin_login }}
                </p>
                <a href="{{ admin_login_url }}" 
                   class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    {{ text_admin_login_link }}
                </a>
            </div>

            <!-- Допълнителна информация -->
            <div class="text-xs text-gray-500 border-t pt-4">
                <p>Това ограничение е временно и ще бъде премахнато скоро.</p>
                <p class="mt-1">HTTP Status: 403 Forbidden</p>
            </div>
        </div>

        <!-- Footer информация -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-500">
                Ако имате въпроси, моля свържете се с администратора.
            </p>
        </div>
    </div>

    <script>
        // Автоматично пренасочване към admin панела след 30 секунди (опционално)
        // setTimeout(function() {
        //     window.location.href = '{{ admin_login_url }}';
        // }, 30000);
    </script>
</body>
</html>
