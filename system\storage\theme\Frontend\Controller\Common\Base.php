<?php

namespace Theme25\Frontend\Controller\Common;

class Base extends \Theme25\Controller {

    public function __construct($registry, $route = '') {
        parent::__construct($registry, $route);
    }

    public function index() {}

    /**
     * Подготвя основните данни за документа
     *
     * @return $this За верижно извикване на методи
     */
    public function prepareDocumentData(&$layoutData) {
        // Определяне на базовия URL според типа на връзката
        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;

        // Добавяне на данните към $layoutData
        $layoutData = array_merge($layoutData, [
            'title' => $this->document->getTitle(),
            'base' => $base,
            'description' => $this->document->getDescription(),
            'keywords' => $this->document->getKeywords(),
            'links' => $this->document->getLinks(),
            'styles' => $this->document->getStyles(),
            'scripts' => $this->document->getScripts('header'),
            'lang' => $this->getLanguageText('code'),
            'direction' => $this->getLanguageText('direction'),
            // 'text_logged' => sprintf($this->getLanguageText('text_logged'), $this->getUserName())
        ]);

        $this->setThemeStyles($layoutData);

        $this->setThemeScripts($layoutData);

        if(!isset($layoutData['scripts'])) {
            $layoutData['scripts'] = $this->document->getScripts('header');
        }
    }

    public function setThemeStyles(&$layoutData) {

        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;

        if(!isset($layoutData['styles'])) {
            $layoutData['styles'] = $this->document->getStyles();
        }

        // CSS файловете са достъпни без проверка за user_token
        $cssUrl = $base . 'frontend_css/frontend.css';

        // Добавяме версия към URL адреса на CSS файла, базирана на времето на последна модификация
        $cssFilePath = DIR_THEME . 'Frontend/View/Css/frontend.css';
        if (file_exists($cssFilePath)) {
            $lastModified = filemtime($cssFilePath);
            $cssUrl .= '?v=' . $lastModified;
        }

        $layoutData['styles'] = array_merge($layoutData['styles'], [
            [
                'href' => $cssUrl,
                'rel' => 'stylesheet',
                'media' => 'screen'
            ]
        ]);
    }

    public function setThemeScripts(&$layoutData) {

        $base = $this->getServer('HTTPS') ? HTTPS_SERVER : HTTP_SERVER;

        if(!isset($layoutData['scripts'])) {
            $layoutData['scripts'] = $this->document->getScripts('header');
        }

        // JavaScript файловете са достъпни без проверка за user_token
        $jsUrl = $base . 'frontend_js/frontend.js';

        // Добавяме версия към URL адреса на JavaScript файла, базирана на времето на последна модификация
        $jsFilePath = DIR_THEME . 'Frontend/View/Javascript/frontend.js';
        if (file_exists($jsFilePath)) {
            $lastModified = filemtime($jsFilePath);
            $jsUrl .= '?v=' . $lastModified;
        }

        $layoutData['scripts'][] = $jsUrl;

        if(!isset($layoutData['scripts_footer'])) {
            $layoutData['scripts_footer'] = $this->document->getScripts('footer');
        }
    }



    //   /**
    //  * Връща името на потребителя
    //  *
    //  * @return string Име на потребителя
    //  */
    // private function getUserName() {
    //     return $this->user->getUserName();
    // }

    // /**
    //  * Връща ID на потребителя
    //  *
    //  * @return int ID на потребителя
    //  */
    // private function getUserId() {    //     return $this->user->getId();
    // }
}
