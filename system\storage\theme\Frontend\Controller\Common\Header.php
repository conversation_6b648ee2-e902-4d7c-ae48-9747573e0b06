<?php

namespace Theme25\Frontend\Controller\Common;

class Header extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/header');
    }

    public function index() {
        // $this->response->addHeader('Cache-Control: no-store, no-cache, must-revalidate');
        // // $this->response->addHeader('Cache-Control: post-check=0, pre-check=0', false);
        // $this->response->addHeader('Pragma: no-cache');
        // $this->response->addHeader('Expires: Thu, 19 Nov 1981 08:52:00 GMT');


        $this->setTitle($this->getLanguageText('heading_title'));


        // // Подготовка на данните с верижно извикване на методи
        // $this->prepareUserData()
        //      ->prepareStoreData()
        //      ->prepareOtherData();

        // Рендиране на шаблона с данните от $this->data
        return $this->loadView('common/header', $this->data);
    }

    

    
}