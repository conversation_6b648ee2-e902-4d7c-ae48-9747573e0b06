<?php

namespace Theme25\Frontend\Controller\Assets;

class Css extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'assets/css');
    }

    /**
     * Обработва заявки за CSS файлове
     *
     * Търси файловете първо в директорията на темата, а след това в стандартната директория.
     * Добавя версия към файла, базирана на времето на последна модификация, за да се избегне кеширане.
     * CSS файловете са достъпни без проверка за user_token, тъй като са необходими за правилното
     * визуализиране на страниците, дори когато потребителят не е логнат.
     */
    public function index() {
        // Проверяваме дали е подаден файл
        if (isset($this->request->get['file'])) {
            $file = basename($this->request->get['file']); // За сигурност вземаме само името на файла

            // Проверяваме за CSS файл в директорията на темата
            $filepath = DIR_THEME . 'Frontend/View/Css/' . $file;

            if (file_exists($filepath) && is_file($filepath)) {
                // Задаваме правилните хедъри за CSS
                $this->sendCssHeaders();

                // Добавяме версия към файла, базирана на времето на последна модификация
                $this->addFileVersionHeader($filepath);

                // Извеждаме съдържанието на файла
                readfile($filepath);
                exit;
            } else {
                // Ако файлът не е намерен в директорията на темата, проверяваме в стандартната директория
                $filepath = DIR_APPLICATION . 'view/stylesheet/' . $file;

                if (file_exists($filepath) && is_file($filepath)) {
                    // Задаваме правилните хедъри за CSS
                    $this->sendCssHeaders();

                    // Добавяме версия към файла, базирана на времето на последна модификация
                    $this->addFileVersionHeader($filepath);

                    // Извеждаме съдържанието на файла
                    readfile($filepath);
                    exit;
                } else {
                    // Ако файлът не е намерен, връщаме 404
                    $this->sendNotFoundResponse();
                }
            }
        } else {
            // Ако не е подаден файл, връщаме 400
            $this->sendBadRequestResponse();
        }
    }

    /**
     * Задава хедъри за CSS файл
     *
     * Инструктира браузъра да кешира CSS файловете за определен период от време,
     * но да проверява дали има промени, използвайки ETag и Last-Modified хедърите.
     * Това позволява ефективно кеширане, но гарантира, че при промяна на файла
     * браузърът ще зареди актуалното съдържание.
     */
    private function sendCssHeaders() {
        header('Content-Type: text/css');

        // Разрешаваме кеширане за 1 седмица (604800 секунди), но изискваме валидация
        header('Cache-Control: public, max-age=604800, must-revalidate');
        header('Pragma: public');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 604800) . ' GMT');
    }

    /**
     * Изпраща отговор 404 Not Found
     */
    private function sendNotFoundResponse() {
        header('HTTP/1.0 404 Not Found');
        echo 'CSS file not found';
    }

    /**
     * Изпраща отговор 400 Bad Request
     */
    private function sendBadRequestResponse() {
        header('HTTP/1.0 400 Bad Request');
        echo 'No file specified';
    }

    /**
     * Добавя версия към файла, базирана на времето на последна модификация
     * и проверява дали браузърът има кеширана версия на файла
     *
     * @param string $filepath Път до файла
     */
    private function addFileVersionHeader($filepath) {
        if (file_exists($filepath)) {
            $lastModified = filemtime($filepath);
            $etag = md5($filepath . $lastModified);

            // Добавяме Last-Modified хедър
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $lastModified) . ' GMT');

            // Добавяме ETag хедър с уникален идентификатор на файла
            header('ETag: "' . $etag . '"');

            // Проверяваме дали браузърът има кеширана версия на файла
            $ifModifiedSince = isset($_SERVER['HTTP_IF_MODIFIED_SINCE']) ?
                strtotime($_SERVER['HTTP_IF_MODIFIED_SINCE']) : false;
            $ifNoneMatch = isset($_SERVER['HTTP_IF_NONE_MATCH']) ?
                trim($_SERVER['HTTP_IF_NONE_MATCH'], '"') : false;

            // Ако браузърът има кеширана версия и файлът не е променен
            if (($ifModifiedSince && $ifModifiedSince >= $lastModified) ||
                ($ifNoneMatch && $ifNoneMatch == $etag)) {
                // Файлът не е променен, връщаме 304 Not Modified
                header('HTTP/1.1 304 Not Modified');
                exit;
            }
        }
    }
}
