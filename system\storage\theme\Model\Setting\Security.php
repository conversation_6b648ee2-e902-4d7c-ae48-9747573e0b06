<?php

namespace Theme25\Model\Setting;

use Theme25\Helper\SettingValidator;

/**
 * Модел за настройки на сигурност
 * Използва новата система за настройки на темата
 */
class Security extends \Theme25\Model\ThemeSetting {

    private $validator;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->settingGroup = 'security';
        $this->validator = new SettingValidator($registry);
        $this->db = $this->getFirstDatabase();
        $this->disableAutoSwitch();
    }

    /**
     * Получава всички настройки за сигурност
     *
     * @return array
     */
    public function getSettings() {

        $settings = $this->getMultipleSettings([
            // IP защита
            'ip_restriction_enabled' => 1,
            'ip_whitelist' => '',
            'ip_blacklist' => '',

            // Политики за пароли
            'password_complexity_enabled' => 1,
            'password_min_length' => 8,
            'password_require_uppercase' => 1,
            'password_require_lowercase' => 1,
            'password_require_numbers' => 1,
            'password_require_symbols' => 0,
            'password_history_enabled' => 1,
            'password_history_count' => 5,
            'password_expiry_enabled' => 0,
            'password_expiry_days' => 90,

            // Логиране и мониторинг
            'security_logging_enabled' => 1,
            'failed_login_logging' => 1,
            'admin_action_logging' => 1,

            // Сигурност при вход
            'max_login_attempts' => 5,
            'lockout_duration' => 30,
            'login_notifications' => 1,

            // Ограничение на достъпа до Frontend частта
            'frontend_access_restriction' => 0
        ], 'security'); // използва новата theme_settings таблица

        return $settings;
    }

    public function getSecuritySetting($key, $default = null) {
        $settings = $this->getSettings();

        F()->log('>>> getSecuritySetting - settings: ' . print_r($settings, true), __FILE__, __LINE__);
        
        if (isset($settings[$key])) {
            return $settings[$key];
        }
        return $default;
    }

    /**
     * Запазва настройките за сигурност
     *
     * @param array $data Данни за запазване
     * @return bool|array True при успех, масив с грешки при неуспех
     */
    public function saveSettings($data) {

        // Подготовка на данните за запазване
        $settingsToSave = [
            // IP защита
            'ip_restriction_enabled' => isset($data['ip_restriction_enabled']) && $data['ip_restriction_enabled'] == 'on' ? 1 : 0,

            // Политики за пароли
            'password_complexity_enabled' => isset($data['password_complexity_enabled']) && $data['password_complexity_enabled'] == 'on' ? 1 : 0,
            'password_min_length' => (int)(isset($data['password_min_length']) ? $data['password_min_length'] : 8),
            'password_require_uppercase' => isset($data['password_require_uppercase']) && $data['password_require_uppercase'] == 'on' ? 1 : 0,
            'password_require_lowercase' => isset($data['password_require_lowercase']) && $data['password_require_lowercase'] == 'on' ? 1 : 0,
            'password_require_numbers' => isset($data['password_require_numbers']) && $data['password_require_numbers'] == 'on' ? 1 : 0,
            'password_require_symbols' => isset($data['password_require_symbols']) && $data['password_require_symbols'] == 'on' ? 1 : 0,
            'password_history_enabled' => isset($data['password_history_enabled']) && $data['password_history_enabled'] == 'on' ? 1 : 0,
            'password_history_count' => (int)(isset($data['password_history_count']) ? $data['password_history_count'] : 5),
            'password_expiry_enabled' => isset($data['password_expiry_enabled']) && $data['password_expiry_enabled'] == 'on' ? 1 : 0,
            'password_expiry_days' => (int)(isset($data['password_expiry_days']) ? $data['password_expiry_days'] : 90),

            // Логиране и мониторинг
            'security_logging_enabled' => isset($data['security_logging_enabled']) && $data['security_logging_enabled'] == 'on' ? 1 : 0,
            'failed_login_logging' => isset($data['failed_login_logging']) && $data['failed_login_logging'] == 'on' ? 1 : 0,
            'admin_action_logging' => isset($data['admin_action_logging']) && $data['admin_action_logging'] == 'on' ? 1 : 0,

            // Сигурност при вход
            'max_login_attempts' => (int)(isset($data['max_login_attempts']) ? $data['max_login_attempts'] : 5),
            'lockout_duration' => (int)(isset($data['lockout_duration']) ? $data['lockout_duration'] : 30),
            'login_notifications' => isset($data['login_notifications']) && $data['login_notifications'] == 'on' ? 1 : 0,

            // Ограничение на достъпа до Frontend частта
            'frontend_access_restriction' => isset($data['frontend_access_restriction']) && $data['frontend_access_restriction'] == 'on' ? 1 : 0
        ];

        if(isset($data['ip_whitelist'])) {
            $settingsToSave['ip_whitelist'] = $data['ip_whitelist'];
        }

        if(isset($data['ip_blacklist'])) {
            $settingsToSave['ip_blacklist'] = $data['ip_blacklist'];
        }

        // Валидация на данните с новата система
        $errors = $this->validateMultipleSettings($settingsToSave, 'security');

        if (!empty($errors)) {
            return $errors;
        }

        // Запазване на настройките с новата система
        return $this->setMultipleSettings($settingsToSave, 'security');
    }

    /**
     * Валидира настройките за сигурност
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    protected function validateSettings($data) {
        $errors = [];

        // Валидация на минимална дължина на паролата
        if (isset($data['password_min_length'])) {
            $minLength = (int)$data['password_min_length'];
            if ($minLength < 4 || $minLength > 128) {
                $errors['password_min_length'] = 'Минималната дължина на паролата трябва да бъде между 4 и 128 символа';
            }
        }

        // Валидация на история на пароли
        if (isset($data['password_history_count'])) {
            $historyCount = (int)$data['password_history_count'];
            if ($historyCount < 1 || $historyCount > 50) {
                $errors['password_history_count'] = 'Броят на паролите в историята трябва да бъде между 1 и 50';
            }
        }

        // Валидация на дни за изтичане на паролата
        if (isset($data['password_expiry_days'])) {
            $expiryDays = (int)$data['password_expiry_days'];
            if ($expiryDays < 1 || $expiryDays > 365) {
                $errors['password_expiry_days'] = 'Дните за изтичане на паролата трябва да бъдат между 1 и 365';
            }
        }

        // Валидация на максимални опити за вход
        if (isset($data['max_login_attempts'])) {
            $maxAttempts = (int)$data['max_login_attempts'];
            if ($maxAttempts < 3 || $maxAttempts > 20) {
                $errors['max_login_attempts'] = 'Максималните опити за вход трябва да бъдат между 3 и 20';
            }
        }

        // Валидация на време за заключване
        if (isset($data['lockout_duration'])) {
            $lockoutDuration = (int)$data['lockout_duration'];
            if ($lockoutDuration < 5 || $lockoutDuration > 1440) {
                $errors['lockout_duration'] = 'Времето за заключване трябва да бъде между 5 и 1440 минути';
            }
        }

        // // Валидация на IP адреси в whitelist и blacklist
        // if (!empty($data['ip_whitelist'])) {
        //     $ipList = explode(',', $data['ip_whitelist']);
        //     foreach ($ipList as $ip) {
        //         $ip = trim($ip);
        //         if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
        //             $errors['ip_whitelist'] = 'Невалиден IP адрес в whitelist: ' . $ip;
        //             break;
        //         }
        //     }
        // }

        // if (!empty($data['ip_blacklist'])) {
        //     $ipList = explode(',', $data['ip_blacklist']);
        //     foreach ($ipList as $ip) {
        //         $ip = trim($ip);
        //         if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
        //             $errors['ip_blacklist'] = 'Невалиден IP адрес в blacklist: ' . $ip;
        //             break;
        //         }
        //     }
        // }

        return $errors;
    }

    // =====================================================
    // IP Whitelist Management Methods
    // =====================================================

    /**
     * Получава всички whitelist IP адреси
     *
     * @param bool $activeOnly Дали да върне само активните IP адреси
     * @return array
     */
    public function getWhitelistIPs($activeOnly = true) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_whitelist`";

        if ($activeOnly) {
            $sql .= " WHERE `status` = 1";
        }

        $sql .= " ORDER BY `created_at` DESC";

        $query = $this->dbQuery($sql);

        return $query->rows;
    }

    /**
     * Добавя нов IP адрес в whitelist
     *
     * @param string $ip_address IP адрес
     * @param string $description Описание
     * @param int $status Статус (1=активен, 0=неактивен)
     * @return int|false ID на новия запис или false при грешка
     */
    public function addWhitelistIP($ip_address, $description = '', $status = 1) {
        // Валидация на IP адреса
        if (!$this->isValidIPAddress($ip_address)) {
            return false;
        }

        try {
            $sql = "INSERT INTO `" . DB_PREFIX . "ip_whitelist`
                    (`ip_address`, `description`, `status`)
                    VALUES ('" . $this->db->escape($ip_address) . "',
                            '" . $this->db->escape($description) . "',
                            " . (int)$status . ")";

            $this->dbQuery($sql);

            return $this->db->getLastId();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Актуализира whitelist IP адрес
     *
     * @param int $id ID на записа
     * @param array $data Данни за актуализиране
     * @return bool
     */
    public function updateWhitelistIP($id, $data) {
        $updates = [];

        if (isset($data['ip_address']) && $this->isValidIPAddress($data['ip_address'])) {
            $updates[] = "`ip_address` = '" . $this->db->escape($data['ip_address']) . "'";
        }

        if (isset($data['description'])) {
            $updates[] = "`description` = '" . $this->db->escape($data['description']) . "'";
        }

        if (isset($data['status'])) {
            $updates[] = "`status` = " . (int)$data['status'];
        }

        if (empty($updates)) {
            return false;
        }

        $updates[] = "`updated_at` = NOW()";

        try {
            $sql = "UPDATE `" . DB_PREFIX . "ip_whitelist`
                    SET " . implode(', ', $updates) . "
                    WHERE `id` = " . (int)$id;

            $this->dbQuery($sql);

            return $this->db->countAffected() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Изтрива whitelist IP адрес
     *
     * @param int $id ID на записа
     * @return bool
     */
    public function deleteWhitelistIP($id) {
        try {
            $sql = "DELETE FROM `" . DB_PREFIX . "ip_whitelist` WHERE `id` = " . (int)$id;
            $this->dbQuery($sql);

            return $this->db->countAffected() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получава whitelist IP адрес по ID
     *
     * @param int $id ID на записа
     * @return array|null
     */
    public function getWhitelistIPById($id) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_whitelist` WHERE `id` = " . (int)$id;
        $query = $this->dbQuery($sql);

        return $query->num_rows > 0 ? $query->row : null;
    }

    // =====================================================
    // IP Blacklist Management Methods
    // =====================================================

    /**
     * Получава всички blacklist IP адреси
     *
     * @param bool $activeOnly Дали да върне само активните IP адреси
     * @return array
     */
    public function getBlacklistIPs($activeOnly = true) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_blacklist`";

        if ($activeOnly) {
            $sql .= " WHERE `status` = 1";
        }

        $sql .= " ORDER BY `created_at` DESC";

        $query = $this->dbQuery($sql);

        return $query->rows;
    }

    /**
     * Добавя нов IP адрес в blacklist
     *
     * @param string $ip_address IP адрес
     * @param string $description Описание
     * @param int $status Статус (1=активен, 0=неактивен)
     * @return int|false ID на новия запис или false при грешка
     */
    public function addBlacklistIP($ip_address, $description = '', $status = 1) {
        // Валидация на IP адреса
        if (!$this->isValidIPAddress($ip_address)) {
            return false;
        }

        try {
            $sql = "INSERT INTO `" . DB_PREFIX . "ip_blacklist`
                    (`ip_address`, `description`, `status`)
                    VALUES ('" . $this->db->escape($ip_address) . "',
                            '" . $this->db->escape($description) . "',
                            " . (int)$status . ")";

            $this->dbQuery($sql);

            return $this->db->getLastId();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Актуализира blacklist IP адрес
     *
     * @param int $id ID на записа
     * @param array $data Данни за актуализиране
     * @return bool
     */
    public function updateBlacklistIP($id, $data) {
        $updates = [];

        if (isset($data['ip_address']) && $this->isValidIPAddress($data['ip_address'])) {
            $updates[] = "`ip_address` = '" . $this->db->escape($data['ip_address']) . "'";
        }

        if (isset($data['description'])) {
            $updates[] = "`description` = '" . $this->db->escape($data['description']) . "'";
        }

        if (isset($data['status'])) {
            $updates[] = "`status` = " . (int)$data['status'];
        }

        if (empty($updates)) {
            return false;
        }

        $updates[] = "`updated_at` = NOW()";

        try {
            $sql = "UPDATE `" . DB_PREFIX . "ip_blacklist`
                    SET " . implode(', ', $updates) . "
                    WHERE `id` = " . (int)$id;

            $this->dbQuery($sql);

            return $this->db->countAffected() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Изтрива blacklist IP адрес
     *
     * @param int $id ID на записа
     * @return bool
     */
    public function deleteBlacklistIP($id) {
        try {
            $sql = "DELETE FROM `" . DB_PREFIX . "ip_blacklist` WHERE `id` = " . (int)$id;
            $this->dbQuery($sql);

            return $this->db->countAffected() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получава blacklist IP адрес по ID
     *
     * @param int $id ID на записа
     * @return array|null
     */
    public function getBlacklistIPById($id) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_blacklist` WHERE `id` = " . (int)$id;
        $query = $this->dbQuery($sql);

        return $query->num_rows > 0 ? $query->row : null;
    }

    // =====================================================
    // Utility Methods
    // =====================================================

    /**
     * Валидира IP адрес (поддържа IPv4, IPv6 и CIDR нотация)
     *
     * @param string $ip_address IP адрес за валидация
     * @return bool
     */
    public function isValidIPAddress($ip_address) {
        // Проверка за CIDR нотация
        if (strpos($ip_address, '/') !== false) {
            list($ip, $prefix) = explode('/', $ip_address, 2);

            // Валидация на IP частта
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                return false;
            }

            // Валидация на prefix частта
            $prefix = (int)$prefix;
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                return $prefix >= 0 && $prefix <= 32;
            } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                return $prefix >= 0 && $prefix <= 128;
            }

            return false;
        }

        // Обикновена IP валидация
        return filter_var($ip_address, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Проверява дали IP адрес е в whitelist
     *
     * @param string $ip_address IP адрес за проверка
     * @return bool
     */
    public function isIPInWhitelist($ip_address) {
        $whitelist_ips = $this->getWhitelistIPs(true);

        foreach ($whitelist_ips as $whitelist_ip) {
            if ($this->matchesIPPattern($ip_address, $whitelist_ip['ip_address'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Проверява дали IP адрес е в blacklist
     *
     * @param string $ip_address IP адрес за проверка
     * @return bool
     */
    public function isIPInBlacklist($ip_address) {
        $blacklist_ips = $this->getBlacklistIPs(true);

        foreach ($blacklist_ips as $blacklist_ip) {
            if ($this->matchesIPPattern($ip_address, $blacklist_ip['ip_address'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Проверява дали IP адрес съвпада с IP pattern (поддържа CIDR)
     *
     * @param string $ip_address IP адрес за проверка
     * @param string $pattern IP pattern (може да бъде CIDR)
     * @return bool
     */
    private function matchesIPPattern($ip_address, $pattern) {
        // Директно съвпадение
        if ($ip_address === $pattern) {
            return true;
        }

        // CIDR проверка
        if (strpos($pattern, '/') !== false) {
            list($network, $prefix) = explode('/', $pattern, 2);

            // Опростена CIDR проверка за IPv4
            if (filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) &&
                filter_var($ip_address, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {

                $network_long = ip2long($network);
                $ip_long = ip2long($ip_address);
                $mask = -1 << (32 - (int)$prefix);

                return ($network_long & $mask) === ($ip_long & $mask);
            }
        }

        return false;
    }

    /**
     * Синхронизира IP данни с legacy настройките
     *
     * @return bool
     */
    public function syncIPDataWithLegacySettings() {
        try {
            // Получаване на whitelist IP адреси
            $whitelist_ips = $this->getWhitelistIPs(true);
            $whitelist_string = implode(',', array_column($whitelist_ips, 'ip_address'));

            // Получаване на blacklist IP адреси
            $blacklist_ips = $this->getBlacklistIPs(true);
            $blacklist_string = implode(',', array_column($blacklist_ips, 'ip_address'));

            // Актуализиране на legacy настройките
            $this->setMultipleSettings([
                'ip_whitelist' => $whitelist_string,
                'ip_blacklist' => $blacklist_string
            ]);

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Мигрира legacy IP данни към новите таблици
     *
     * @return bool
     */
    public function migrateLegacyIPData() {
        try {
            $settings = $this->getMultipleSettings(['ip_whitelist', 'ip_blacklist']);

            // Мигриране на whitelist данни
            if (!empty($settings['ip_whitelist'])) {
                $ips = array_filter(array_map('trim', preg_split('/[,\n]/', $settings['ip_whitelist'])));
                foreach ($ips as $ip) {
                    if ($this->isValidIPAddress($ip)) {
                        $this->addWhitelistIP($ip, 'Мигриран от стари настройки', 1);
                    }
                }
            }

            // Мигриране на blacklist данни
            if (!empty($settings['ip_blacklist'])) {
                $ips = array_filter(array_map('trim', preg_split('/[,\n]/', $settings['ip_blacklist'])));
                foreach ($ips as $ip) {
                    if ($this->isValidIPAddress($ip)) {
                        $this->addBlacklistIP($ip, 'Мигриран от стари настройки', 1);
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получаване на whitelist IP адреси с pagination
     */
    public function getWhitelistIPsPaginated($page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;

        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_whitelist`
                ORDER BY `id` DESC
                LIMIT " . (int)$offset . ", " . (int)$perPage;

        $query = $this->dbQuery($sql);

        return $query->rows;
    }

    /**
     * Получаване на blacklist IP адреси с pagination
     */
    public function getBlacklistIPsPaginated($page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;

        $sql = "SELECT * FROM `" . DB_PREFIX . "ip_blacklist`
                ORDER BY `id` DESC
                LIMIT " . (int)$offset . ", " . (int)$perPage;

        $query = $this->dbQuery($sql);

        return $query->rows;
    }

    /**
     * Получаване на общия брой whitelist IP адреси
     */
    public function getTotalWhitelistIPs() {
        $sql = "SELECT COUNT(*) as total FROM `" . DB_PREFIX . "ip_whitelist`";
        $query = $this->dbQuery($sql);

        return (int)$query->row['total'];
    }

    /**
     * Получаване на общия брой blacklist IP адреси
     */
    public function getTotalBlacklistIPs() {
        $sql = "SELECT COUNT(*) as total FROM `" . DB_PREFIX . "ip_blacklist`";
        $query = $this->dbQuery($sql);

        return (int)$query->row['total'];
    }
}
