/**
 * Основен JavaScript модул за административния панел
 */
(function() {
    'use strict';

    // Инициализация на модула
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на всички компоненти
        FrontendModule.init();
    });

    // Основен обект с функционалности
    var FrontendModule = {
        // Конфигурация
        config: {

        },

        init: function() {
            // this.initSidebar();
            // this.initModals();
            // this.initFilters();
            // this.initDropdowns();
        },

       

        /**
         * Прави AJAX заявка с използване на fetch API
         * @param {string} url - URL адрес за заявката
         * @param {FormData} formData - Данни за изпращане
         * @param {Function} successCallback - Функция за успешен отговор
         * @param {Function} errorCallback - Функция за грешка
         */
        makeAjaxRequest: function(url, formData, successCallback, errorCallback) {
            // Проверка дали formData е обект, но не е FormData
            if (formData && typeof formData === 'object' && !(formData instanceof FormData)) {
                // Ако е обект, преобразуваме го в JSON стринг
                formData = JSON.stringify(formData);
            }
            
            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && typeof successCallback === 'function') {
                    successCallback(data);
                } else if (typeof errorCallback === 'function') {
                    errorCallback(new Error(data.message));
                }
            })
            .catch(error => {
                if (typeof errorCallback === 'function') {
                    errorCallback(error);
                }
            });
        },

        showNotification: function(message, type = 'info') {
            if (typeof this.showAlert === 'function') {
                this.showAlert(type, message);
            }
        },
    };

    // Експортиране на модула за глобален достъп
    window.FrontendModule = FrontendModule;

})();